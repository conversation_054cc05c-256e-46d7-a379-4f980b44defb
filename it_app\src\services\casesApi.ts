/**
 * AI 案例相關 API 服務
 */

import { apiClient } from './api';

// 案例數據類型定義
export interface CaseItem {
  id: number;
  title: string;
  subtitle?: string;
  description: string;
  technologies?: string[];
  category: string;
  industry?: string;
  client_name?: string;
  project_duration?: string;
  cover_image?: string;
  client_rating?: number;
  is_featured: boolean;
  view_count: number;
  created_at: string;
}

export interface CaseDetail extends CaseItem {
  detailed_description?: string;
  project_budget?: number;
  achievements?: string[];
  benefits?: string;
  roi_percentage?: number;
  gallery_images?: string[];
  demo_video?: string;
  testimonials: Array<{
    id: number;
    client_name: string;
    client_title?: string;
    client_company?: string;
    client_avatar?: string;
    content: string;
    rating: number;
    is_featured: boolean;
    created_at: string;
  }>;
  updated_at: string;
}

export interface CaseStats {
  total_cases: number;
  featured_cases: number;
  total_views: number;
  average_rating?: number;
  categories: Array<{
    category: string;
    count: number;
    display_name: string;
  }>;
  technologies: Array<{
    technology: string;
    count: number;
    display_name: string;
  }>;
}

export interface CasesListParams {
  page?: number;
  size?: number;
  category?: string;
  technology?: string;
  industry?: string;
  featured?: boolean;
  search?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// API 服務類
export class CasesApiService {
  /**
   * 獲取案例列表
   */
  static async getCases(params: CasesListParams = {}): Promise<PaginatedResponse<CaseItem>> {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.size) searchParams.append('size', params.size.toString());
    if (params.category) searchParams.append('category', params.category);
    if (params.technology) searchParams.append('technology', params.technology);
    if (params.industry) searchParams.append('industry', params.industry);
    if (params.featured) searchParams.append('featured', params.featured.toString());
    if (params.search) searchParams.append('search', params.search);

    const response = await apiClient.get(`/cases?${searchParams.toString()}`);
    return response.data.data;
  }

  /**
   * 獲取案例詳情
   */
  static async getCaseDetail(id: number): Promise<CaseDetail> {
    const response = await apiClient.get(`/cases/${id}`);
    return response.data.data;
  }

  /**
   * 獲取精選案例
   */
  static async getFeaturedCases(limit: number = 6): Promise<CaseItem[]> {
    const response = await apiClient.get(`/cases/featured?limit=${limit}`);
    return response.data.data;
  }

  /**
   * 獲取案例統計信息
   */
  static async getCaseStats(): Promise<CaseStats> {
    const response = await apiClient.get('/cases/stats');
    return response.data.data;
  }

  /**
   * 獲取案例分類列表
   */
  static async getCaseCategories(): Promise<Array<{
    category: string;
    count: number;
    display_name: string;
  }>> {
    const response = await apiClient.get('/cases/categories');
    return response.data.data;
  }

  /**
   * 獲取案例技術列表
   */
  static async getCaseTechnologies(): Promise<Array<{
    technology: string;
    count: number;
    display_name: string;
  }>> {
    const response = await apiClient.get('/cases/technologies');
    return response.data.data;
  }
}

// 導出默認實例
export const casesApi = CasesApiService;
