@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局樣式重置和基礎設置 */
@layer base {
  * {
    box-sizing: border-box;
  }

  html {
    -webkit-text-size-adjust: 100%;
    font-feature-settings: normal;
    font-variation-settings: normal;
    tab-size: 4;
  }

  body {
    margin: 0;
    line-height: inherit;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* 移動端優化 */
  body {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* 允許文本選擇的元素 */
  input, textarea, [contenteditable] {
    -webkit-user-select: text;
    -khtml-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }

  /* 滾動條樣式 */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
  }
}

/* 自定義組件樣式 */
@layer components {
  /* 移動端安全區域 */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* 觸摸反饋 */
  .touch-feedback {
    @apply active:scale-95 transform transition-transform duration-150;
  }

  /* 卡片陰影 */
  .card-shadow {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  .card-shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  /* 漸變背景 */
  .gradient-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  }

  /* 毛玻璃效果 */
  .backdrop-blur {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
}

/* 工具類 */
@layer utilities {
  /* 隱藏滾動條但保持滾動功能 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* 文本省略 */
  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 移動端專用 */
  .mobile-only {
    @media (min-width: 481px) {
      display: none !important;
    }
  }

  .desktop-only {
    @media (max-width: 480px) {
      display: none !important;
    }
  }
} 