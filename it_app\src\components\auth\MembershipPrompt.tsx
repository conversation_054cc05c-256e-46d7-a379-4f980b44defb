import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  User, 
  Star, 
  Heart, 
  MessageCircle, 
  Settings,
  ArrowRight,
  X
} from 'lucide-react';

interface MembershipPromptProps {
  isOpen: boolean;
  onClose: () => void;
  feature: string;
  description?: string;
}

const MembershipPrompt: React.FC<MembershipPromptProps> = ({
  isOpen,
  onClose,
  feature,
  description
}) => {
  const navigate = useNavigate();

  if (!isOpen) return null;

  const handleLogin = () => {
    onClose();
    navigate('/login');
  };

  const memberBenefits = [
    {
      icon: Heart,
      title: '收藏案例',
      description: '保存感興趣的 AI 案例'
    },
    {
      icon: MessageCircle,
      title: '提交需求',
      description: '直接聯繫我們討論項目'
    },
    {
      icon: Settings,
      title: '個人資料',
      description: '管理個人信息和偏好'
    },
    {
      icon: Star,
      title: '專屬服務',
      description: '享受會員專屬優惠'
    }
  ];

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* 頭部 */}
        <div className="relative p-6 pb-4">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <User className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-xl font-bold text-gray-900 mb-2">
              加入會員享受更多功能
            </h2>
            <p className="text-gray-600 text-sm">
              {description || `使用「${feature}」功能需要登入會員帳號`}
            </p>
          </div>
        </div>

        {/* 會員權益 */}
        <div className="px-6 pb-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">會員專享權益</h3>
          <div className="space-y-3">
            {memberBenefits.map((benefit, index) => {
              const IconComponent = benefit.icon;
              return (
                <div key={index} className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                    <IconComponent className="w-5 h-5 text-primary-600" />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 text-sm">
                      {benefit.title}
                    </div>
                    <div className="text-xs text-gray-500">
                      {benefit.description}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* 登入選項 */}
        <div className="px-6 pb-6">
          <div className="space-y-3">
            <button
              onClick={handleLogin}
              className="w-full bg-gradient-to-r from-primary-600 to-primary-700 text-white py-3 px-4 rounded-lg font-medium flex items-center justify-center space-x-2 hover:from-primary-700 hover:to-primary-800 transition-all"
            >
              <span>立即登入</span>
              <ArrowRight className="w-4 h-4" />
            </button>
            
            <div className="text-center">
              <span className="text-sm text-gray-500">還沒有帳號？</span>
              <button
                onClick={() => {
                  onClose();
                  navigate('/register');
                }}
                className="text-sm text-primary-600 font-medium ml-1 hover:text-primary-700"
              >
                立即註冊
              </button>
            </div>
          </div>
        </div>

        {/* 底部說明 */}
        <div className="bg-gray-50 px-6 py-4 rounded-b-2xl">
          <p className="text-xs text-gray-500 text-center">
            註冊完全免費，您可以隨時取消會員資格
          </p>
        </div>
      </div>
    </div>
  );
};

export default MembershipPrompt;
