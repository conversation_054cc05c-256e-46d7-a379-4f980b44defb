"""
用戶相關 Pydantic 模型
"""

from typing import Optional
from pydantic import BaseModel, EmailStr, Field, validator
import re


class UserUpdate(BaseModel):
    """用戶更新模型"""
    
    nickname: Optional[str] = Field(
        None, 
        min_length=1, 
        max_length=100,
        description="暱稱"
    )
    avatar_url: Optional[str] = Field(
        None, 
        max_length=255,
        description="頭像URL"
    )


class PasswordUpdate(BaseModel):
    """密碼更新模型"""
    
    current_password: str = Field(..., description="當前密碼")
    new_password: str = Field(
        ..., 
        min_length=8, 
        max_length=128,
        description="新密碼（8-128字符，包含大小寫字母和數字）"
    )
    
    @validator("new_password")
    def validate_new_password(cls, v):
        """驗證新密碼強度"""
        if not re.search(r"[a-z]", v):
            raise ValueError("密碼必須包含小寫字母")
        if not re.search(r"[A-Z]", v):
            raise ValueError("密碼必須包含大寫字母")
        if not re.search(r"\d", v):
            raise ValueError("密碼必須包含數字")
        return v


class UserProfile(BaseModel):
    """用戶資料模型"""
    
    id: int = Field(..., description="用戶ID")
    username: str = Field(..., description="用戶名")
    email: str = Field(..., description="電子郵件")
    nickname: Optional[str] = Field(None, description="暱稱")
    avatar_url: Optional[str] = Field(None, description="頭像URL")
    login_provider: str = Field(..., description="登入方式")
    
    # 統計信息
    completed_projects: int = Field(default=0, description="完成項目數")
    customer_rating: float = Field(default=0.0, description="客戶評分")
    growth_index: int = Field(default=0, description="成長指數")
    
    class Config:
        from_attributes = True
