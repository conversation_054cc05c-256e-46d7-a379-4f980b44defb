## 目錄結構\n├ backend/\n│   ├app/\n│   │   ├api/ \n│   │   ├core/ #核心功能,整个项目运行的基础功能和全局配置\n│   │   ├models/ #定义数据结构和数据库表结构    資料庫相關        \n│   │   └services/  #封装具体的业务操作\n\n## 1. API設計\n-设置API路由和控制器\n-实现请求验证\n-添加身份验证中间件\n-配置速率限制\n-实现错误处理\n-添加日志和监控\n### 1.1 用户注册\n- **POST** `/api/register`\n- **請求格式**：\n```json\n{\n  \"username\": \"example_user\",\n  \"password\": \"example_password\",\n  \"contact\": \"1234567890\", // 手机号码或邮箱\n  \"verification_code\": \"123456\", // 短信验证码或邮箱验证码\n  \"auth_type\": \"sms\" // 认证方式，sms 或 email\n}\n```\n- **回應格式**：\n```json\n{\n  \"code\": 200,\n  \"message\": \"注册成功\",\n  \"data\": {\n    \"user_id\": 1,\n    \"token\": \"example_token\"\n  }\n}\n```\n\n### 1.2 用户登录\n- **POST** `/api/login`\n- **請求格式**：\n```json\n{\n  \"username\": \"example_user\",\n  \"password\": \"example_password\"\n}\n```\n- **回應格式**：\n```json\n{\n  \"code\": 200,\n  \"message\": \"登录成功\",\n  \"data\": {\n    \"user_id\": 1,\n    \"token\": \"example_token\"\n  }\n}\n```\n\n### 1.3 第三方登录\n- **POST** `/api/login/third-party`\n- **請求格式**：\n```json\n{\n  \"platform\": \"wechat\", // 第三方平台，如 wechat、qq\n  \"code\": \"example_code\"\n}\n```\n- **回應格式**：\n```json\n{\n  \"code\": 200,\n  \"message\": \"登录成功\",\n  \"data\": {\n    \"user_id\": 1,\n    \"token\": \"example_token\"\n  }\n}\n```\n\n### 1.4 重置密码\n- **POST** `/api/reset-password`\n- **請求格式**：\n```json\n{\n  \"contact\": \"1234567890\", // 手机号码或邮箱\n  \"verification_code\": \"123456\",\n  \"new_password\": \"new_example_password\"\n}\n```\n- **回應格式**：\n```json\n{\n  \"code\": 200,\n  \"message\": \"密码重置成功\"\n}\n```\n\n### 1.5 提交案件需求\n- **POST** `/api/cases/submit`\n- **請求格式**：\n```json\n{\n  \"user_id\": 1,\n  \"case_type\": \"AI开发\",\n  \"description\": \"详细描述项目的应用场景、数据来源等需求，以及预期要达到的成果等信息\",\n  \"expected_result\": \"预期成果描述\"\n}\n```\n- **回應格式**：\n```json\n{\n  \"code\": 200,\n  \"message\": \"案件需求提交成功\",\n  \"data\": {\n    \"case_id\": 1,\n    \"category\": \"AI开发类\"\n  }\n}\n```\n\n### 1.6 查看案件列表（接案方）\n- **GET** `/api/cases/available`\n- **請求格式**：无\n- **回應格式**：\n```json\n{\n  \"code\": 200,\n  \"message\": \"获取可接案件列表成功\",\n  \"data\": [\n    {\n      \"case_id\": 1,\n      \"case_type\": \"AI开发\",\n      \"description\": \"详细描述\",\n      \"expected_result\": \"预期成果\"\n    },\n    {\n      \"case_id\": 2,\n      \"case_type\": \"数据分析\",\n      \"description\": \"详细描述\",\n      \"expected_result\": \"预期成果\"\n    }\n  ]\n}\n```\n\n### 1.7 接案方响应案件\n- **POST** `/api/cases/respond`\n- **請求格式**：\n```json\n{\n  \"user_id\": 2, // 接案方用户 ID\n  \"case_id\": 1\n}\n```\n- **回應格式**：\n```json\n{\n  \"code\": 200,\n  \"message\": \"接案方响应成功\"\n}\n```\n\n### 1.8 进入沟通页面\n- **GET** `/api/communication/{case_id}`\n- **請求格式**：无\n- **回應格式**：\n```json\n{\n  \"code\": 200,\n  \"message\": \"获取沟通页面信息成功\",\n  \"data\": {\n    \"case_id\": 1,\n    \"messages\": [\n      {\n        \"sender_id\": 1,\n        \"content\": \"你好，这是案件需求\",\n        \"send_time\": \"2024-01-15 10:00:00\"\n      },\n      {\n        \"sender_id\": 2,\n        \"content\": \"我明白了，会尽快处理\",\n        \"send_time\": \"2024-01-15 10:10:00\"\n      }\n    ]\n  }\n}\n```\n\n### 1.9 发送消息\n- **POST** `/api/communication/send`\n- **請求格式**：\n```json\n{\n  \"sender_id\": 1,\n  \"receiver_id\": 2,\n  \"case_id\": 1,\n  \"content\": \"新的消息内容\",\n  \"message_type\": \"text\" // text、image、file\n}\n```\n- **回應格式**：\n```json\n{\n  \"code\": 200,\n  \"message\": \"消息发送成功\"\n}\n```\n\n### 1.10 接案方上传代码\n- **POST** `/api/cases/upload-code`\n- **請求格式**：\n```json\n{\n  \"user_id\": 2,\n  \"case_id\": 1,\n  \"code_url\": \"http://example.com/code.zip\"\n}\n```\n- **回應格式**：\n```json\n{\n  \"code\": 200,\n  \"message\": \"代码上传成功\"\n}\n```\n\n### 1.11 用户查看下载代码\n- **GET** `/api/cases/download-code/{case_id}`\n- **請求格式**：无\n- **回應格式**：\n```json\n{\n  \"code\": 200,\n  \"message\": \"获取代码文件成功\",\n  \"data\": {\n    \"code_url\": \"http://example.com/code.zip\"\n  }\n}\n```\n\n### 1.12 用户确认代码\n- **POST** `/api/cases/confirm-code`\n- **請求格式**：\n```json\n{\n  \"user_id\": 1,\n  \"case_id\": 1,\n  \"is_approved\": true\n}\n```\n- **回應格式**：\n```json\n{\n  \"code\": 200,\n  \"message\": \"代码确认成功\"\n}\n```\n\n### 1.13 用户支付\n- **POST** `/api/payment`\n- **請求格式**：\n```json\n{\n  \"user_id\": 1,\n  \"case_id\": 1,\n  \"payment_method\": \"PayPal\",\n  \"amount\": 100.00\n}\n```\n- **回應格式**：\n```json\n{\n  \"code\": 200,\n  \"message\": \"支付成功\",\n  \"data\": {\n    \"transaction_id\": \"1234567890\"\n  }\n}\n```\n\n### 1.14 用户评价接案方\n- **POST** `/api/evaluation`\n- **請求格式**：\n```json\n{\n  \"user_id\": 1,\n  \"receiver_id\": 2,\n  \"case_id\": 1,\n  \"rating\": 5,\n  \"comment\": \"非常满意，代码质量高\"\n}\n```\n- **回應格式**：\n```json\n{\n  \"code\": 200,\n  \"message\": \"评价成功\"\n}\n```\n\n### 1.15 查看案件进度\n- **GET** `/api/cases/status/{case_id}`\n- **請求格式**：无\n- **回應格式**：\n```json\n{\n  \"code\": 200,\n  \"message\": \"获取案件进度成功\",\n  \"data\": {\n    \"case_id\": 1,\n    \"status\": \"已完成\",\n    \"progress\": 100\n  }\n}\n```\n\n### 1.16 用户修改个人资料\n- **PUT** `/api/users/profile`\n- **請求格式**：\n```json\n{\n  \"user_id\": 1,\n  \"nickname\": \"new_nickname\",\n  \"avatar\": \"http://example.com/avatar.jpg\",\n  \"contact\": \"new_contact\"\n}\n```\n- **回應格式**：\n```json\n{\n  \"code\": 200,\n  \"message\": \"个人资料修改成功\"\n}\n```\n\n### 1.17 用户修改安全设置\n- **PUT** `/api/users/security`\n- **請求格式**：\n```json\n{\n  \"user_id\": 1,\n  \"password\": \"new_password\",\n  \"enable_gesture\": true,\n  \"enable_fingerprint\": true\n}\n```\n- **回應格式**：\n```json\n{\n  \"code\": 200,\n  \"message\": \"安全设置修改成功\"\n}\n```\n\n### 1.18 用户修改通知偏好\n- **PUT** `/api/users/notification`\n- **請求格式**：\n```json\n{\n  \"user_id\": 1,\n  \"receive_case_notice\": true,\n  \"receive_message_reminder\": false\n}\n```\n- **回應格式**：\n```json\n{\n  \"code\": 200,\n  \"message\": \"通知偏好修改成功\"\n}\n```\n\n### 1.19 用户定制体验\n- **PUT** `/api/users/customize`\n- **請求格式**：\n```json\n{\n  \"user_id\": 1,\n  \"theme_color\": \"blue\",\n  \"font_size\": \"large\"\n}\n```\n- **回應格式**：\n```json\n{\n  \"code\": 200,\n  \"message\": \"定制体验修改成功\"\n}\n```\n\n### 1.20 查看交易记录\n- **GET** `/api/users/transactions`\n- **請求格式**：无\n- **回應格式**：\n```json\n{\n  \"code\": 200,\n  \"message\": \"获取交易记录成功\",\n  \"data\": [\n    {\n      \"transaction_id\": \"1234567890\",\n      \"case_id\": 1,\n      \"amount\": 100.00,\n      \"payment_time\": \"2024-01-15 10:00:00\"\n    }\n  ]\n}\n```\n\n## 2. 資料庫設計\n-设置数据库模型和架构\n-实现数据关系\n-创建数据库迁移\n-添加数据验证\n-优化查询性能\n-实现数据缓存\n### 2.1 用户表（users）\n| 欄位         | 型別           | 說明           | 約束條件       |\n|--------------|----------------|----------------|----------------|\n| id           | bigint         | 主鍵           | AUTO_INCREMENT |\n| username     | varchar(50)    | 用户名         | UNIQUE         |\n| password     | varchar(100)   | 密码(加密)     | NOT NULL       |\n| contact      | varchar(100)   | 联系方式（手机号码或邮箱） | UNIQUE |\n| auth_type    | varchar(10)    | 认证方式       |                |\n| is_verified  | tinyint        | 是否验证       | DEFAULT 0      |\n| role         | varchar(20)    | 用户角色（user 或 contractor） | NOT NULL |\n| nickname     | varchar(50)    | 昵称           |                |\n| avatar       | varchar(255)   | 头像链接       |                |\n| enable_gesture | tinyint      | 是否启用手势密码 | DEFAULT 0      |\n| enable_fingerprint | tinyint    | 是否启用指纹识别 | DEFAULT 0      |\n| receive_case_notice | tinyint | 是否接收案件通知 | DEFAULT 1      |\n| receive_message_reminder | tinyint | 是否接收消息提醒 | DEFAULT 1      |\n| theme_color  | varchar(20)    | 主题颜色       |                |\n| font_size    | varchar(10)    | 字体大小       |                |\n| created_at   | timestamp      | 创建时间       | DEFAULT NOW()  |\n| updated_at   | timestamp      | 更新时间       | ON UPDATE NOW()|\n\n### 2.2 案件表（cases）\n| 欄位         | 型別           | 說明           | 約束條件       |\n|--------------|----------------|----------------|----------------|\n| id           | bigint         | 主鍵           | AUTO_INCREMENT |\n| user_id      | bigint         | 发布案件的用户 ID | FOREIGN KEY |\n| case_type    | varchar(50)    | 案件类型       | NOT NULL       |\n| description  | text           | 案件描述       | NOT NULL       |\n| expected_result | text        | 预期成果       | NOT NULL       |\n| category     | varchar(50)    | 案件分类       |                |\n| status       | varchar(20)    | 案件状态（待处理/处理中/已完成） | DEFAULT '待处理' |\n| created_at   | timestamp      | 创建时间       | DEFAULT NOW()  |\n| updated_at   | timestamp      | 更新时间       | ON UPDATE NOW()|\n\n### 2.3 接案记录表（case_responses）\n| 欄位         | 型別           | 說明           | 約束條件       |\n|--------------|----------------|----------------|----------------|\n| id           | bigint         | 主鍵           | AUTO_INCREMENT |\n| user_id      | bigint         | 接案方用户 ID | FOREIGN KEY |\n| case_id      | bigint         | 案件 ID | FOREIGN KEY |\n| response_time | timestamp      | 响应时间       | DEFAULT NOW()  |\n\n### 2.4 消息表（messages）\n| 欄位         | 型別           | 說明           | 約束條件       |\n|--------------|----------------|----------------|----------------|\n| id           | bigint         | 主鍵           | AUTO_INCREMENT |\n| sender_id    | bigint         | 发送者用户 ID | FOREIGN KEY |\n| receiver_id  | bigint         | 接收者用户 ID | FOREIGN KEY |\n| case_id      | bigint         | 关联案件 ID | FOREIGN KEY |\n| content      | text           | 消息内容       | NOT NULL       |\n| message_type | varchar(10)    | 消息类型（text、image、file） | NOT NULL |\n| send_time    | timestamp      | 发送时间       | DEFAULT NOW()  |\n\n### 2.5 代码表（codes）\n| 欄位         | 型別           | 說明           | 約束條件       |\n|--------------|----------------|----------------|----------------|\n| id           | bigint         | 主鍵           | AUTO_INCREMENT |\n| user_id      | bigint         | 接案方用户 ID | FOREIGN KEY |\n| case_id      | bigint         | 案件 ID | FOREIGN KEY |\n| code_url     | varchar(255)   | 代码链接       | NOT NULL       |\n| upload_time  | timestamp      | 上传时间       | DEFAULT NOW()  |\n\n### 2.6 交易记录表（transactions）\n| 欄位         | 型別           | 說明           | 約束條件       |\n|--------------|----------------|----------------|----------------|\n| id           | bigint         | 主鍵           | AUTO_INCREMENT |\n| user_id      | bigint         | 用户 ID | FOREIGN KEY |\n| case_id      | bigint         | 案件 ID | FOREIGN KEY |\n| amount       | decimal(10, 2) | 交易金额       | NOT NULL       |\n| payment_method | varchar(20)    | 支付方式       | NOT NULL       |\n| transaction_id | varchar(50)    | 交易 ID | UNIQUE |\n| payment_time | timestamp      | 支付时间       | DEFAULT NOW()  |\n\n### 2.7 评价表（evaluations）\n| 欄位         | 型別           | 說明           | 約束條件       |\n|--------------|----------------|----------------|----------------|\n| id           | bigint         | 主鍵           | AUTO_INCREMENT |\n| user_id      | bigint         | 