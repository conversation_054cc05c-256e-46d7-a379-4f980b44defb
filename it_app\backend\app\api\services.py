"""
AI 服務相關 API 路由
"""

from decimal import Decimal
from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from app.core.database import get_db
from app.core.dependencies import get_current_user_optional
from app.schemas.common import ResponseModel, PaginationParams, PaginatedResponse
from app.models.service import AIService
from app.models.user import User

router = APIRouter()


# AI 服務響應模型
from pydantic import BaseModel
from datetime import datetime


class AIServiceResponse(BaseModel):
    """AI 服務響應模型"""
    id: int
    name: str
    description: Optional[str]
    category: str
    price_min: Decimal
    price_max: Decimal
    is_active: bool
    created_at: datetime
    
    class Config:
        from_attributes = True


@router.get("", response_model=ResponseModel)
async def get_services(
    page: int = Query(1, ge=1, description="頁碼"),
    size: int = Query(20, ge=1, le=100, description="每頁數量"),
    category: Optional[str] = Query(None, description="服務分類篩選"),
    search: Optional[str] = Query(None, description="搜索關鍵詞"),
    db: AsyncSession = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional)
) -> Any:
    """
    獲取 AI 服務列表
    支持分頁、搜索和分類篩選
    """
    try:
        # 構建查詢
        query = select(AIService).where(AIService.is_active == True)
        
        # 分類篩選
        if category:
            query = query.where(AIService.category == category)
        
        # 搜索篩選
        if search:
            search_pattern = f"%{search}%"
            query = query.where(
                (AIService.name.ilike(search_pattern)) |
                (AIService.description.ilike(search_pattern))
            )
        
        # 計算總數
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 分頁查詢
        pagination = PaginationParams(page=page, size=size)
        query = query.offset(pagination.offset).limit(pagination.size)
        
        result = await db.execute(query)
        services = result.scalars().all()
        
        # 轉換為響應模型
        service_list = [AIServiceResponse.model_validate(service) for service in services]
        
        # 創建分頁響應
        paginated_data = PaginatedResponse.create(
            items=service_list,
            total=total,
            page=page,
            size=size
        )
        
        return ResponseModel(
            success=True,
            message="獲取服務列表成功",
            data=paginated_data
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="獲取服務列表失敗"
        )


@router.get("/{service_id}", response_model=ResponseModel)
async def get_service_detail(
    service_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional)
) -> Any:
    """
    獲取 AI 服務詳情
    """
    try:
        # 查詢服務
        stmt = select(AIService).where(
            (AIService.id == service_id) & 
            (AIService.is_active == True)
        )
        result = await db.execute(stmt)
        service = result.scalar_one_or_none()
        
        if not service:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="服務不存在"
            )
        
        service_data = AIServiceResponse.model_validate(service)
        
        return ResponseModel(
            success=True,
            message="獲取服務詳情成功",
            data=service_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="獲取服務詳情失敗"
        )


@router.get("/categories/list", response_model=ResponseModel)
async def get_service_categories(
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    獲取服務分類列表
    """
    try:
        # 查詢所有活躍服務的分類
        stmt = select(AIService.category).where(
            AIService.is_active == True
        ).distinct()
        
        result = await db.execute(stmt)
        categories = result.scalars().all()
        
        # 分類映射（可以從配置文件或數據庫讀取）
        category_mapping = {
            "ai-search": "智能搜索",
            "ai-platform": "AI平台", 
            "ai-media": "媒體處理",
            "ai-chat": "對話系統",
            "ai-vision": "計算機視覺",
            "ai-analytics": "數據分析"
        }
        
        category_list = [
            {
                "id": category,
                "name": category_mapping.get(category, category),
                "code": category
            }
            for category in categories
        ]
        
        return ResponseModel(
            success=True,
            message="獲取分類列表成功",
            data=category_list
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="獲取分類列表失敗"
        )
