'use client'

import { useRef, useMemo, useEffect, useState } from 'react'
import { useFrame } from '@react-three/fiber'
import { Points, PointMaterial } from '@react-three/drei'
import * as THREE from 'three'
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor'

interface ParticleSystemProps {
  count?: number
  color?: string
  size?: number
  speed?: number
  spread?: number
}

export function ParticleSystem({
  count = 1000,
  color = '#3b82f6',
  size = 0.01,
  speed = 0.5,
  spread = 5
}: ParticleSystemProps) {
  const pointsRef = useRef<THREE.Points>(null)
  const { settings, isLowPerformance } = usePerformanceMonitor()

  // 根據性能設置調整粒子參數
  const [adjustedCount, setAdjustedCount] = useState(count)
  const [adjustedSpeed, setAdjustedSpeed] = useState(speed)
  const [adjustedSize, setAdjustedSize] = useState(size)

  useEffect(() => {
    if (settings) {
      // 根據性能設置調整粒子數量
      const performanceMultiplier = settings.particleCount / 100 // 基準值 100
      setAdjustedCount(Math.min(count, Math.max(10, Math.floor(count * performanceMultiplier))))
      setAdjustedSpeed(speed * settings.animationSpeed / 0.015) // 基準速度 0.015
      setAdjustedSize(size * settings.renderScale)
    }
  }, [settings, count, speed, size])

  // 生成粒子位置（使用調整後的數量）
  const positions = useMemo(() => {
    const positions = new Float32Array(adjustedCount * 3)

    for (let i = 0; i < adjustedCount; i++) {
      const i3 = i * 3
      positions[i3] = (Math.random() - 0.5) * spread
      positions[i3 + 1] = (Math.random() - 0.5) * spread
      positions[i3 + 2] = (Math.random() - 0.5) * spread
    }

    return positions
  }, [adjustedCount, spread])

  // 生成粒子速度（使用調整後的參數）
  const velocities = useMemo(() => {
    const velocities = new Float32Array(adjustedCount * 3)

    for (let i = 0; i < adjustedCount; i++) {
      const i3 = i * 3
      velocities[i3] = (Math.random() - 0.5) * adjustedSpeed
      velocities[i3 + 1] = (Math.random() - 0.5) * adjustedSpeed
      velocities[i3 + 2] = (Math.random() - 0.5) * adjustedSpeed
    }

    return velocities
  }, [adjustedCount, adjustedSpeed])

  // 動畫循環（性能優化版本）
  useFrame((state, delta) => {
    if (!pointsRef.current) return

    // 在低性能設備上降低更新頻率
    if (isLowPerformance && state.clock.elapsedTime % 2 > 1) return

    const positions = pointsRef.current.geometry.attributes.position.array as Float32Array

    for (let i = 0; i < adjustedCount; i++) {
      const i3 = i * 3

      // 更新位置
      positions[i3] += velocities[i3] * delta
      positions[i3 + 1] += velocities[i3 + 1] * delta
      positions[i3 + 2] += velocities[i3 + 2] * delta

      // 邊界檢查和重置
      if (Math.abs(positions[i3]) > spread / 2) {
        positions[i3] = (Math.random() - 0.5) * spread
        velocities[i3] = (Math.random() - 0.5) * adjustedSpeed
      }
      if (Math.abs(positions[i3 + 1]) > spread / 2) {
        positions[i3 + 1] = (Math.random() - 0.5) * spread
        velocities[i3 + 1] = (Math.random() - 0.5) * adjustedSpeed
      }
      if (Math.abs(positions[i3 + 2]) > spread / 2) {
        positions[i3 + 2] = (Math.random() - 0.5) * spread
        velocities[i3 + 2] = (Math.random() - 0.5) * adjustedSpeed
      }
    }

    pointsRef.current.geometry.attributes.position.needsUpdate = true

    // 旋轉整個粒子系統（在低性能設備上減慢）
    const rotationSpeed = isLowPerformance ? 0.0005 : 0.001
    pointsRef.current.rotation.y += rotationSpeed
  })

  return (
    <Points ref={pointsRef} positions={positions} stride={3} frustumCulled={false}>
      <PointMaterial
        transparent
        color={color}
        size={adjustedSize}
        sizeAttenuation={true}
        depthWrite={false}
        opacity={0.6}
        blending={THREE.AdditiveBlending}
      />
    </Points>
  )
}
