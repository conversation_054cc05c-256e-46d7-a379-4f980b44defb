'use client'

import { useRef, useMemo } from 'react'
import { useFrame } from '@react-three/fiber'
import { Points, PointMaterial } from '@react-three/drei'
import * as THREE from 'three'

interface ParticleSystemProps {
  count?: number
  color?: string
  size?: number
  speed?: number
  spread?: number
}

export function ParticleSystem({ 
  count = 1000,
  color = '#3b82f6',
  size = 0.01,
  speed = 0.5,
  spread = 5
}: ParticleSystemProps) {
  const pointsRef = useRef<THREE.Points>(null)

  // 生成粒子位置
  const positions = useMemo(() => {
    const positions = new Float32Array(count * 3)
    
    for (let i = 0; i < count; i++) {
      const i3 = i * 3
      positions[i3] = (Math.random() - 0.5) * spread
      positions[i3 + 1] = (Math.random() - 0.5) * spread
      positions[i3 + 2] = (Math.random() - 0.5) * spread
    }
    
    return positions
  }, [count, spread])

  // 生成粒子速度
  const velocities = useMemo(() => {
    const velocities = new Float32Array(count * 3)
    
    for (let i = 0; i < count; i++) {
      const i3 = i * 3
      velocities[i3] = (Math.random() - 0.5) * speed
      velocities[i3 + 1] = (Math.random() - 0.5) * speed
      velocities[i3 + 2] = (Math.random() - 0.5) * speed
    }
    
    return velocities
  }, [count, speed])

  useFrame((state, delta) => {
    if (!pointsRef.current) return

    const positions = pointsRef.current.geometry.attributes.position.array as Float32Array

    for (let i = 0; i < count; i++) {
      const i3 = i * 3
      
      // 更新位置
      positions[i3] += velocities[i3] * delta
      positions[i3 + 1] += velocities[i3 + 1] * delta
      positions[i3 + 2] += velocities[i3 + 2] * delta

      // 邊界檢查和重置
      if (Math.abs(positions[i3]) > spread / 2) {
        positions[i3] = (Math.random() - 0.5) * spread
        velocities[i3] = (Math.random() - 0.5) * speed
      }
      if (Math.abs(positions[i3 + 1]) > spread / 2) {
        positions[i3 + 1] = (Math.random() - 0.5) * spread
        velocities[i3 + 1] = (Math.random() - 0.5) * speed
      }
      if (Math.abs(positions[i3 + 2]) > spread / 2) {
        positions[i3 + 2] = (Math.random() - 0.5) * spread
        velocities[i3 + 2] = (Math.random() - 0.5) * speed
      }
    }

    pointsRef.current.geometry.attributes.position.needsUpdate = true
  })

  return (
    <Points ref={pointsRef} positions={positions} stride={3} frustumCulled={false}>
      <PointMaterial
        transparent
        color={color}
        size={size}
        sizeAttenuation={true}
        depthWrite={false}
        opacity={0.6}
        blending={THREE.AdditiveBlending}
      />
    </Points>
  )
}
