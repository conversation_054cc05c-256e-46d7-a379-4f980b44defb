'use client'

import { useRef, useMemo } from 'react'
import { useFrame } from '@react-three/fiber'
import { Ring, Text } from '@react-three/drei'
import * as THREE from 'three'

interface Progress3DProps {
  progress: number // 0-100
  size?: number
  color?: string
  backgroundColor?: string
  position?: [number, number, number]
  animated?: boolean
}

export function Progress3D({ 
  progress,
  size = 1,
  color = '#3b82f6',
  backgroundColor = '#e5e7eb',
  position = [0, 0, 0],
  animated = true
}: Progress3DProps) {
  const progressRingRef = useRef<THREE.Mesh>(null)
  const backgroundRingRef = useRef<THREE.Mesh>(null)

  // 計算進度角度
  const progressAngle = useMemo(() => {
    return (progress / 100) * Math.PI * 2
  }, [progress])

  useFrame((state) => {
    if (!animated) return

    if (progressRingRef.current) {
      // 輕微的脈動效果
      const scale = 1 + Math.sin(state.clock.elapsedTime * 2) * 0.02
      progressRingRef.current.scale.setScalar(scale)
    }

    if (backgroundRingRef.current) {
      // 緩慢旋轉
      backgroundRingRef.current.rotation.z += 0.005
    }
  })

  return (
    <group position={position}>
      {/* 背景環 */}
      <Ring
        ref={backgroundRingRef}
        args={[size * 0.8, size, 0, Math.PI * 2, 64]}
      >
        <meshBasicMaterial 
          color={backgroundColor} 
          transparent 
          opacity={0.3}
          side={THREE.DoubleSide}
        />
      </Ring>

      {/* 進度環 */}
      <Ring
        ref={progressRingRef}
        args={[size * 0.8, size, -Math.PI / 2, progressAngle, 64]}
        rotation={[0, 0, 0]}
      >
        <meshBasicMaterial 
          color={color} 
          transparent 
          opacity={0.8}
          side={THREE.DoubleSide}
        />
      </Ring>

      {/* 進度文字 */}
      <Text
        position={[0, 0, 0.01]}
        fontSize={size * 0.3}
        color={color}
        anchorX="center"
        anchorY="middle"
        font="/fonts/Inter-Bold.woff"
      >
        {Math.round(progress)}%
      </Text>

      {/* 中心點 */}
      <mesh position={[0, 0, 0.005]}>
        <circleGeometry args={[size * 0.1, 32]} />
        <meshBasicMaterial color={color} />
      </mesh>

      {/* 發光效果 */}
      <pointLight
        position={[0, 0, 0.5]}
        intensity={0.3}
        color={color}
        distance={size * 3}
      />
    </group>
  )
}
