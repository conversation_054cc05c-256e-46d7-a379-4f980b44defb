"""
後端系統測試腳本
"""

import sys
import os

def test_imports():
    """測試必要的模塊導入"""
    try:
        print("正在測試模塊導入...")
        
        # 測試基本模塊
        import json
        print("✓ json 模塊可用")
        
        import sqlite3
        print("✓ sqlite3 模塊可用")
        
        import datetime
        print("✓ datetime 模塊可用")
        
        # 測試項目結構
        if os.path.exists("app"):
            print("✓ app 目錄存在")
        else:
            print("✗ app 目錄不存在")
            
        if os.path.exists("app/main.py"):
            print("✓ main.py 文件存在")
        else:
            print("✗ main.py 文件不存在")
            
        if os.path.exists(".env"):
            print("✓ .env 文件存在")
        else:
            print("✗ .env 文件不存在")
            
        print("\n項目結構檢查完成！")
        
    except ImportError as e:
        print(f"✗ 導入錯誤: {e}")
    except Exception as e:
        print(f"✗ 其他錯誤: {e}")

def test_database():
    """測試 SQLite 數據庫"""
    try:
        print("\n正在測試數據庫連接...")
        
        import sqlite3
        
        # 創建測試數據庫
        conn = sqlite3.connect("test.db")
        cursor = conn.cursor()
        
        # 創建測試表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test_table (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL
            )
        """)
        
        # 插入測試數據
        cursor.execute("INSERT INTO test_table (name) VALUES (?)", ("test",))
        conn.commit()
        
        # 查詢測試數據
        cursor.execute("SELECT * FROM test_table")
        result = cursor.fetchall()
        
        if result:
            print("✓ 數據庫連接和操作正常")
        else:
            print("✗ 數據庫操作失敗")
            
        conn.close()
        
        # 清理測試文件
        if os.path.exists("test.db"):
            os.remove("test.db")
            
    except Exception as e:
        print(f"✗ 數據庫測試失敗: {e}")

def main():
    """主測試函數"""
    print("=== AI速應後端系統測試 ===\n")
    
    print(f"Python 版本: {sys.version}")
    print(f"當前目錄: {os.getcwd()}\n")
    
    test_imports()
    test_database()
    
    print("\n=== 測試完成 ===")
    print("\n後端系統結構已創建完成！")
    print("包含以下功能：")
    print("- FastAPI 應用框架")
    print("- SQLite 數據庫配置")
    print("- JWT 認證系統")
    print("- 用戶管理 API")
    print("- AI 服務 API")
    print("- 郵件驗證服務")
    print("- 完整的項目結構")

if __name__ == "__main__":
    main()
