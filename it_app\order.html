<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI速應 - 订单</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://unpkg.com/lucide-static/font/lucide.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f7fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            padding-bottom: 20px;
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }
        .tab {
            padding: 8px 16px;
            font-size: 14px;
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            color: #6366f1;
            border-bottom: 2px solid #6366f1;
            font-weight: 500;
        }
        .status-badge {
            border-radius: 12px;
            padding: 2px 8px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-badge.pending {
            background: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
        }
        .status-badge.in-progress {
            background: rgba(99, 102, 241, 0.1);
            color: #6366f1;
        }
        .status-badge.completed {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }
        .order-card {
            transition: all 0.3s ease;
        }
        .order-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="p-4">
    <!-- 顶部标题 -->
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold mb-1">我的訂單</h1>
        <div class="flex items-center">
            <i class="lucide lucide-search text-gray-600 mr-3"></i>
            <i class="lucide lucide-filter text-gray-600"></i>
        </div>
    </div>

    <!-- 订单标签 -->
    <div class="flex justify-between mb-6 border-b">
        <div class="tab active">全部</div>
        <div class="tab text-gray-600">待付款</div>
        <div class="tab text-gray-600">進行中</div>
        <div class="tab text-gray-600">已完成</div>
        <div class="tab text-gray-600">已取消</div>
    </div>

    <!-- 订单列表 -->
    <div class="space-y-4">
        <!-- 订单1 -->
        <div class="order-card glass-card p-4">
            <div class="flex justify-between items-start mb-3">
                <div class="flex items-center">
                    <img src="https://images.unsplash.com/photo-1684391223740-c8a4d036ee8c?q=80&w=50&auto=format&fit=crop" class="w-10 h-10 rounded-lg object-cover mr-3" alt="Dify AI">
                    <h3 class="font-semibold text-lg">Dify AI 定制服务</h3>
                </div>
                <div class="status-badge in-progress">进行中</div>
            </div>
            <div class="flex justify-between text-sm text-gray-600 mb-2 space-y-1">
                <div>订单编号: AI202405120001</div>
                <div>下单时间: 2024-05-12</div>
            </div>
            <div class="border-t border-gray-100 pt-3 flex justify-between items-center">
                <div>
                    <span class="text-gray-600 text-sm font-medium">订单金额:</span>
                    <span class="text-indigo-600 font-semibold ml-1">¥2999.00</span>
                </div>
                <div class="flex space-x-2">
                    <button class="px-4 py-1.5 text-sm rounded-full border border-gray-200 text-gray-600">查看详情</button>
                    <button class="px-4 py-1.5 text-sm rounded-full bg-indigo-600 text-white">联系客服</button>
                </div>
            </div>
        </div>

        <!-- 订单2 -->
        <div class="order-card glass-card p-4">
            <div class="flex justify-between items-start mb-3">
                <div class="flex items-center">
                    <img src="https://images.unsplash.com/photo-1655720031554-a929595ffad7?q=80&w=50&auto=format&fit=crop" class="w-10 h-10 rounded-lg object-cover mr-3" alt="Coze机器人">
                    <h3 class="font-semibold">Coze机器人开发</h3>
                </div>
                <div class="status-badge pending">待付款</div>
            </div>
            <div class="flex justify-between text-sm text-gray-600 mb-2 space-y-1">
                <div>订单编号: AI202405110003</div>
                <div>下单时间: 2024-05-11</div>
            </div>
            <div class="border-t border-gray-100 pt-3 flex justify-between items-center">
                <div>
                    <span class="text-gray-600 text-sm font-medium">订单金额:</span>
                    <span class="text-indigo-600 font-semibold ml-1">¥1599.00</span>
                </div>
                <div class="flex space-x-2">
                    <button class="px-4 py-1.5 text-sm rounded-full border border-gray-200 text-gray-600">取消订单</button>
                    <button class="px-4 py-1.5 text-sm rounded-full bg-indigo-600 text-white">立即支付</button>
                </div>
            </div>
        </div>

        <!-- 订单3 -->
        <div class="order-card glass-card p-4">
            <div class="flex justify-between items-start mb-3">
                <div class="flex items-center">
                    <img src="https://images.unsplash.com/photo-1678995632928-298d05d41671?q=80&w=50&auto=format&fit=crop" class="w-10 h-10 rounded-lg object-cover mr-3" alt="n8n工作流">
                    <h3 class="font-semibold">n8n自动化工作流</h3>
                </div>
                <div class="status-badge completed">已完成</div>
            </div>
            <div class="flex justify-between text-sm text-gray-600 mb-2 space-y-1">
                <div>订单编号: AI202405010002</div>
                <div>下单时间: 2024-05-01</div>
            </div>
            <div class="border-t border-gray-100 pt-3 flex justify-between items-center">
                <div>
                    <span class="text-gray-600 text-sm font-medium">订单金额:</span>
                    <span class="text-indigo-600 font-semibold ml-1">¥1899.00</span>
                </div>
                <div class="flex space-x-2">
                    <button class="px-4 py-1.5 text-sm rounded-full border border-gray-200 text-gray-600">查看详情</button>
                    <button class="px-4 py-1.5 text-sm rounded-full bg-indigo-600 text-white">评价服务</button>
                </div>
            </div>
        </div>

        <!-- 订单4 -->
        <div class="order-card glass-card p-4">
            <div class="flex justify-between items-start mb-3">
                <div class="flex items-center">
                    <img src="https://images.unsplash.com/photo-1673187236223-3d09568d1910?q=80&w=50&auto=format&fit=crop" class="w-10 h-10 rounded-lg object-cover mr-3" alt="AI图像生成">
                    <h3 class="font-semibold">AI图像生成服务</h3>
                </div>
                <div class="status-badge completed">已完成</div>
            </div>
            <div class="flex justify-between text-sm text-gray-600 mb-2 space-y-1">
                <div>订单编号: AI202404250001</div>
                <div>下单时间: 2024-04-25</div>
            </div>
            <div class="border-t border-gray-100 pt-3 flex justify-between items-center">
                <div>
                    <span class="text-gray-600 text-sm font-medium">订单金额:</span>
                    <span class="text-indigo-600 font-semibold ml-1">¥1299.00</span>
                </div>
                <div class="flex space-x-2">
                    <button class="px-4 py-1.5 text-sm rounded-full border border-gray-200 text-gray-600">查看详情</button>
                    <button class="px-4 py-1.5 text-sm rounded-full border border-indigo-600 text-indigo-600">再次购买</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 空状态 -->
    <div class="hidden flex flex-col items-center justify-center py-10">
        <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <i class="lucide lucide-package-x text-gray-400 text-3xl"></i>
        </div>
        <p class="text-gray-500 mb-4">暂无相关订单</p>
        <button class="px-6 py-2 bg-indigo-600 text-white rounded-full text-sm">浏览服务</button>
    </div>
</body>
</html>