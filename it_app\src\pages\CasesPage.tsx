import React, { useState, useEffect, Suspense } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Search,
  Filter,
  Star,
  Eye,
  Calendar,
  TrendingUp,
  Award,
  ChevronRight,
  Grid,
  List,
  Heart
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useMembership } from '@/hooks/useMembership';
import MobileLayout from '@/components/layout/MobileLayout';
import MembershipPrompt from '@/components/auth/MembershipPrompt';
import { View, Common } from '@/components/canvas/View';
import { Card3D } from '@/components/canvas/Card3D';
import { ParticleSystem } from '@/components/canvas/ParticleSystem';

// 案例數據類型定義
interface CaseItem {
  id: number;
  title: string;
  subtitle?: string;
  description: string;
  technologies?: string[];
  category: string;
  industry?: string;
  client_name?: string;
  project_duration?: string;
  cover_image?: string;
  client_rating?: number;
  is_featured: boolean;
  view_count: number;
  created_at: string;
}

interface CaseStats {
  total_cases: number;
  featured_cases: number;
  total_views: number;
  average_rating?: number;
  categories: Array<{
    category: string;
    count: number;
    display_name: string;
  }>;
  technologies: Array<{
    technology: string;
    count: number;
    display_name: string;
  }>;
}

const CasesPage: React.FC = () => {
  const navigate = useNavigate();
  const { state } = useAuth();
  const {
    isMember,
    showPrompt,
    promptFeature,
    promptDescription,
    requireMembership,
    closePrompt
  } = useMembership();
  
  // 狀態管理
  const [cases, setCases] = useState<CaseItem[]>([]);
  const [stats, setStats] = useState<CaseStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedTechnology, setSelectedTechnology] = useState<string>('');
  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // 模擬 API 調用
  useEffect(() => {
    fetchCases();
    fetchStats();
  }, [currentPage, selectedCategory, selectedTechnology, showFeaturedOnly, searchTerm]);

  const fetchCases = async () => {
    setLoading(true);
    try {
      // 模擬 API 調用
      const mockCases: CaseItem[] = [
        {
          id: 1,
          title: "智慧醫療診斷系統",
          subtitle: "基於深度學習的醫學影像分析平台",
          description: "為某三甲醫院開發的AI輔助診斷系統，能夠快速準確地分析醫學影像，提高診斷效率和準確性。",
          technologies: ["Deep Learning", "Computer Vision", "TensorFlow", "Medical AI"],
          category: "ai-vision",
          industry: "醫療健康",
          client_name: "某三甲醫院",
          project_duration: "6個月",
          cover_image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400",
          client_rating: 4.95,
          is_featured: true,
          view_count: 1250,
          created_at: "2024-01-15"
        },
        {
          id: 2,
          title: "智能客服機器人",
          subtitle: "24/7全天候智能客戶服務解決方案",
          description: "為大型電商平台開發的智能客服系統，支持多輪對話、情感分析和自動問題解決。",
          technologies: ["NLP", "ChatGPT", "LangChain", "Knowledge Graph"],
          category: "ai-chat",
          industry: "電子商務",
          client_name: "某大型電商平台",
          project_duration: "4個月",
          cover_image: "https://images.unsplash.com/photo-1531746790731-6c087fecd65a?w=400",
          client_rating: 4.88,
          is_featured: true,
          view_count: 980,
          created_at: "2024-02-01"
        },
        {
          id: 3,
          title: "企業知識管理平台",
          subtitle: "基於RAG技術的智能知識檢索系統",
          description: "為大型企業構建的智能知識管理平台，實現企業文檔的智能檢索和問答。",
          technologies: ["RAG", "Vector Database", "LangChain", "Elasticsearch"],
          category: "ai-search",
          industry: "企業服務",
          client_name: "某大型製造企業",
          project_duration: "5個月",
          cover_image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400",
          client_rating: 4.92,
          is_featured: true,
          view_count: 756,
          created_at: "2024-01-20"
        },
        {
          id: 4,
          title: "智能視頻內容分析",
          subtitle: "AI驅動的視頻內容理解與處理平台",
          description: "為媒體公司開發的智能視頻分析系統，自動識別內容、生成標籤和摘要。",
          technologies: ["Computer Vision", "Video Analysis", "PyTorch", "AI Media"],
          category: "ai-media",
          industry: "媒體娛樂",
          client_name: "某知名媒體公司",
          project_duration: "7個月",
          cover_image: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400",
          client_rating: 4.85,
          is_featured: false,
          view_count: 634,
          created_at: "2024-01-10"
        },
        {
          id: 5,
          title: "智能數據分析平台",
          subtitle: "自動化商業智能分析解決方案",
          description: "為金融機構開發的智能數據分析平台，提供自動化的數據洞察和預測分析。",
          technologies: ["Machine Learning", "Data Analytics", "Python", "Business Intelligence"],
          category: "ai-analytics",
          industry: "金融服務",
          client_name: "某大型銀行",
          project_duration: "8個月",
          cover_image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400",
          client_rating: 4.90,
          is_featured: true,
          view_count: 892,
          created_at: "2024-01-05"
        },
        {
          id: 6,
          title: "智能推薦系統",
          subtitle: "個性化內容推薦引擎",
          description: "為在線教育平台開發的智能推薦系統，提供個性化的學習內容推薦。",
          technologies: ["Recommendation System", "Collaborative Filtering", "Deep Learning"],
          category: "ai-recommendation",
          industry: "在線教育",
          client_name: "某在線教育平台",
          project_duration: "4個月",
          cover_image: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400",
          client_rating: 4.78,
          is_featured: false,
          view_count: 543,
          created_at: "2024-02-10"
        }
      ];

      setCases(mockCases);
      setTotalPages(1);
    } catch (error) {
      console.error('獲取案例失敗:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const mockStats: CaseStats = {
        total_cases: 6,
        featured_cases: 4,
        total_views: 5055,
        average_rating: 4.88,
        categories: [
          { category: "ai-vision", count: 1, display_name: "計算機視覺" },
          { category: "ai-chat", count: 1, display_name: "對話系統" },
          { category: "ai-search", count: 1, display_name: "智能搜索" },
          { category: "ai-media", count: 1, display_name: "媒體處理" },
          { category: "ai-analytics", count: 1, display_name: "數據分析" },
          { category: "ai-recommendation", count: 1, display_name: "推薦系統" }
        ],
        technologies: [
          { technology: "Deep Learning", count: 3, display_name: "深度學習" },
          { technology: "Machine Learning", count: 2, display_name: "機器學習" },
          { technology: "NLP", count: 2, display_name: "自然語言處理" },
          { technology: "Computer Vision", count: 2, display_name: "計算機視覺" }
        ]
      };
      setStats(mockStats);
    } catch (error) {
      console.error('獲取統計失敗:', error);
    }
  };

  const handleCaseClick = (caseId: number) => {
    navigate(`/cases/${caseId}`);
  };

  const handleFavoriteCase = (caseId: number) => {
    requireMembership(
      '收藏案例',
      '收藏感興趣的案例，方便日後查看',
      () => {
        // 執行收藏邏輯
        console.log('收藏案例:', caseId);
        // TODO: 實現收藏功能
      }
    );
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'ai-vision': 'bg-purple-100 text-purple-800',
      'ai-chat': 'bg-blue-100 text-blue-800',
      'ai-search': 'bg-green-100 text-green-800',
      'ai-media': 'bg-orange-100 text-orange-800',
      'ai-analytics': 'bg-red-100 text-red-800',
      'ai-recommendation': 'bg-yellow-100 text-yellow-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  const getCategoryName = (category: string) => {
    const names: Record<string, string> = {
      'ai-vision': '計算機視覺',
      'ai-chat': '對話系統',
      'ai-search': '智能搜索',
      'ai-media': '媒體處理',
      'ai-analytics': '數據分析',
      'ai-recommendation': '推薦系統'
    };
    return names[category] || category;
  };

  return (
    <MobileLayout enable3D={true}>
      <div className="flex flex-col h-full bg-gray-50 relative">
        {/* 3D 背景效果 */}
        <View className="absolute inset-0 opacity-5">
          <Suspense fallback={null}>
            <ParticleSystem 
              count={150}
              color="#3b82f6"
              size={0.001}
              speed={0.02}
              spread={8}
            />
            <Common />
          </Suspense>
        </View>

        {/* 頁面頭部 */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 px-6 py-6 text-white relative z-10">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold">AI 成功案例</h1>
              <p className="text-primary-100 text-sm">探索我們的項目成果</p>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-lg transition-colors ${
                  viewMode === 'grid' ? 'bg-white/20' : 'bg-white/10'
                }`}
              >
                <Grid className="w-5 h-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-lg transition-colors ${
                  viewMode === 'list' ? 'bg-white/20' : 'bg-white/10'
                }`}
              >
                <List className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* 統計信息 */}
          {stats && (
            <div className="grid grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold">{stats.total_cases}</div>
                <div className="text-xs text-primary-200">總案例</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{stats.featured_cases}</div>
                <div className="text-xs text-primary-200">精選案例</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{stats.total_views}</div>
                <div className="text-xs text-primary-200">總瀏覽</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{stats.average_rating?.toFixed(1)}</div>
                <div className="text-xs text-primary-200">平均評分</div>
              </div>
            </div>
          )}
        </div>

        {/* 搜索和篩選 */}
        <div className="px-6 py-4 bg-white border-b relative z-10">
          <div className="flex items-center space-x-3 mb-3">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="搜索案例..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
            <button className="p-2 border border-gray-300 rounded-lg">
              <Filter className="w-4 h-4 text-gray-600" />
            </button>
          </div>

          {/* 快速篩選 */}
          <div className="flex items-center space-x-2 overflow-x-auto">
            <button
              onClick={() => setShowFeaturedOnly(!showFeaturedOnly)}
              className={`px-3 py-1 rounded-full text-sm whitespace-nowrap transition-colors ${
                showFeaturedOnly 
                  ? 'bg-primary-100 text-primary-800' 
                  : 'bg-gray-100 text-gray-600'
              }`}
            >
              <Award className="w-3 h-3 inline mr-1" />
              精選案例
            </button>
            {stats?.categories.slice(0, 3).map((category) => (
              <button
                key={category.category}
                onClick={() => setSelectedCategory(
                  selectedCategory === category.category ? '' : category.category
                )}
                className={`px-3 py-1 rounded-full text-sm whitespace-nowrap transition-colors ${
                  selectedCategory === category.category
                    ? 'bg-primary-100 text-primary-800'
                    : 'bg-gray-100 text-gray-600'
                }`}
              >
                {category.display_name}
              </button>
            ))}
          </div>
        </div>

        {/* 案例列表 */}
        <div className="flex-1 px-6 py-4 relative z-10">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
              <p className="text-gray-500 mt-2">載入中...</p>
            </div>
          ) : (
            <div className={viewMode === 'grid' ? 'grid grid-cols-1 gap-4' : 'space-y-4'}>
              {cases.map((caseItem, index) => (
                <div key={caseItem.id} className="h-32">
                  <View className="w-full h-full">
                    <Suspense fallback={null}>
                      <Card3D
                        position={[0, 0, 0]}
                        rotation={[0, 0, 0]}
                        scale={[1, 1, 1]}
                        onClick={() => handleCaseClick(caseItem.id)}
                      />
                      <Common />
                    </Suspense>
                  </View>
                  
                  {/* 2D 內容覆蓋層 */}
                  <div 
                    className="absolute inset-0 bg-white rounded-lg shadow-sm border border-gray-200 p-4 cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => handleCaseClick(caseItem.id)}
                  >
                    <div className="flex items-start space-x-3 h-full">
                      {/* 案例圖片 */}
                      <div className="w-20 h-20 bg-gray-200 rounded-lg flex-shrink-0 overflow-hidden">
                        {caseItem.cover_image ? (
                          <img 
                            src={caseItem.cover_image} 
                            alt={caseItem.title}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center">
                            <TrendingUp className="w-8 h-8 text-white" />
                          </div>
                        )}
                      </div>

                      {/* 案例信息 */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-1">
                          <h3 className="font-semibold text-gray-900 text-sm truncate">
                            {caseItem.title}
                          </h3>
                          <div className="flex items-center space-x-1">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleFavoriteCase(caseItem.id);
                              }}
                              className="p-1 rounded-full hover:bg-gray-100 transition-colors"
                            >
                              <Heart className="w-4 h-4 text-gray-400 hover:text-red-500" />
                            </button>
                            {caseItem.is_featured && (
                              <Award className="w-4 h-4 text-yellow-500" />
                            )}
                          </div>
                        </div>
                        
                        {caseItem.subtitle && (
                          <p className="text-xs text-gray-600 mb-2 line-clamp-1">
                            {caseItem.subtitle}
                          </p>
                        )}
                        
                        <p className="text-xs text-gray-500 mb-2 line-clamp-2">
                          {caseItem.description}
                        </p>

                        {/* 底部信息 */}
                        <div className="flex items-center justify-between text-xs text-gray-400">
                          <div className="flex items-center space-x-3">
                            <span className={`px-2 py-1 rounded-full text-xs ${getCategoryColor(caseItem.category)}`}>
                              {getCategoryName(caseItem.category)}
                            </span>
                            {caseItem.client_rating && (
                              <div className="flex items-center">
                                <Star className="w-3 h-3 text-yellow-400 mr-1" />
                                <span>{caseItem.client_rating.toFixed(1)}</span>
                              </div>
                            )}
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="flex items-center">
                              <Eye className="w-3 h-3 mr-1" />
                              <span>{caseItem.view_count}</span>
                            </div>
                            <ChevronRight className="w-4 h-4" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 會員提示 */}
      <MembershipPrompt
        isOpen={showPrompt}
        onClose={closePrompt}
        feature={promptFeature}
        description={promptDescription}
      />
    </MobileLayout>
  );
};

export default CasesPage;
