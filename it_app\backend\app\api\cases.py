"""
AI 案例相關 API 路由
"""

from typing import Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.dependencies import get_current_active_user
from app.schemas.common import ResponseModel, PaginationParams, PaginatedResponse
from app.schemas.case import (
    AICaseListResponse,
    AICaseDetailResponse,
    CaseStatsResponse,
    CaseCategoryResponse,
    CaseTechnologyResponse
)
from app.services.case_service import CaseService
from app.models.user import User

router = APIRouter()


async def get_case_service(db: AsyncSession = Depends(get_db)) -> CaseService:
    """獲取案例服務實例"""
    return CaseService(db)


@router.get("", response_model=ResponseModel)
async def get_cases(
    page: int = Query(1, ge=1, description="頁碼"),
    size: int = Query(12, ge=1, le=50, description="每頁數量"),
    category: Optional[str] = Query(None, description="分類篩選"),
    technology: Optional[str] = Query(None, description="技術篩選"),
    industry: Optional[str] = Query(None, description="行業篩選"),
    featured: bool = Query(False, description="只顯示精選案例"),
    search: Optional[str] = Query(None, description="搜索關鍵詞"),
    current_user: User = Depends(get_current_active_user),
    case_service: CaseService = Depends(get_case_service)
) -> Any:
    """
    獲取 AI 案例列表
    需要用戶登入
    """
    try:
        pagination = PaginationParams(page=page, size=size)
        
        cases, total = await case_service.get_cases(
            pagination=pagination,
            category=category,
            technology=technology,
            industry=industry,
            featured_only=featured,
            search=search
        )
        
        # 轉換為響應模型
        case_list = [AICaseListResponse.model_validate(case) for case in cases]
        
        # 創建分頁響應
        paginated_data = PaginatedResponse.create(
            items=case_list,
            total=total,
            page=page,
            size=size
        )
        
        return ResponseModel(
            success=True,
            message="獲取案例列表成功",
            data=paginated_data
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="獲取案例列表失敗"
        )


@router.get("/featured", response_model=ResponseModel)
async def get_featured_cases(
    limit: int = Query(6, ge=1, le=20, description="限制數量"),
    current_user: User = Depends(get_current_active_user),
    case_service: CaseService = Depends(get_case_service)
) -> Any:
    """
    獲取精選案例
    需要用戶登入
    """
    try:
        cases = await case_service.get_featured_cases(limit=limit)
        
        # 轉換為響應模型
        case_list = [AICaseListResponse.model_validate(case) for case in cases]
        
        return ResponseModel(
            success=True,
            message="獲取精選案例成功",
            data=case_list
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="獲取精選案例失敗"
        )


@router.get("/stats", response_model=ResponseModel)
async def get_case_stats(
    current_user: User = Depends(get_current_active_user),
    case_service: CaseService = Depends(get_case_service)
) -> Any:
    """
    獲取案例統計信息
    需要用戶登入
    """
    try:
        stats = await case_service.get_case_stats()
        
        return ResponseModel(
            success=True,
            message="獲取案例統計成功",
            data=stats
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="獲取案例統計失敗"
        )


@router.get("/categories", response_model=ResponseModel)
async def get_case_categories(
    current_user: User = Depends(get_current_active_user),
    case_service: CaseService = Depends(get_case_service)
) -> Any:
    """
    獲取案例分類列表
    需要用戶登入
    """
    try:
        categories = await case_service.get_case_categories()
        
        return ResponseModel(
            success=True,
            message="獲取分類列表成功",
            data=categories
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="獲取分類列表失敗"
        )


@router.get("/technologies", response_model=ResponseModel)
async def get_case_technologies(
    current_user: User = Depends(get_current_active_user),
    case_service: CaseService = Depends(get_case_service)
) -> Any:
    """
    獲取案例技術列表
    需要用戶登入
    """
    try:
        technologies = await case_service.get_case_technologies()
        
        return ResponseModel(
            success=True,
            message="獲取技術列表成功",
            data=technologies
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="獲取技術列表失敗"
        )


@router.get("/{case_id}", response_model=ResponseModel)
async def get_case_detail(
    case_id: int,
    current_user: User = Depends(get_current_active_user),
    case_service: CaseService = Depends(get_case_service)
) -> Any:
    """
    獲取案例詳情
    需要用戶登入
    """
    try:
        case = await case_service.get_case_by_id(case_id)
        
        if not case:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="案例不存在"
            )
        
        case_data = AICaseDetailResponse.model_validate(case)
        
        return ResponseModel(
            success=True,
            message="獲取案例詳情成功",
            data=case_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="獲取案例詳情失敗"
        )
