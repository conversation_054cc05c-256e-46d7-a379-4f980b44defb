import { apiRequest } from './api';
import { 
  Case, 
  CaseSubmitRequest, 
  CaseResponse,
  CodeSubmission,
  CodeUploadRequest,
  PaymentRequest,
  Transaction,
  EvaluationRequest,
  Evaluation
} from '@/types';

// 案件管理服務
export const caseService = {
  // 提交案件需求
  submitCase: async (caseData: CaseSubmitRequest) => {
    try {
      return await apiRequest.post('/cases/submit', caseData);
    } catch (error) {
      console.error('Submit case error:', error);
      throw error;
    }
  },

  // 獲取所有可接案件（接案方視角）
  getAvailableCases: async (): Promise<Case[]> => {
    try {
      const response = await apiRequest.get<Case[]>('/cases/available');
      return response.data || [];
    } catch (error) {
      console.error('Get available cases error:', error);
      throw error;
    }
  },

  // 獲取用戶案件列表
  getUserCases: async (userId: number): Promise<Case[]> => {
    try {
      const response = await apiRequest.get<Case[]>(`/cases/user/${userId}`);
      return response.data || [];
    } catch (error) {
      console.error('Get user cases error:', error);
      throw error;
    }
  },

  // 獲取案件詳情
  getCaseDetail: async (caseId: number): Promise<Case> => {
    try {
      const response = await apiRequest.get<Case>(`/cases/${caseId}`);
      return response.data!;
    } catch (error) {
      console.error('Get case detail error:', error);
      throw error;
    }
  },

  // 接案方響應案件
  respondToCase: async (userId: number, caseId: number) => {
    try {
      return await apiRequest.post('/cases/respond', {
        userId,
        caseId
      });
    } catch (error) {
      console.error('Respond to case error:', error);
      throw error;
    }
  },

  // 獲取案件響應列表
  getCaseResponses: async (caseId: number): Promise<CaseResponse[]> => {
    try {
      const response = await apiRequest.get<CaseResponse[]>(`/cases/${caseId}/responses`);
      return response.data || [];
    } catch (error) {
      console.error('Get case responses error:', error);
      throw error;
    }
  },

  // 上傳代碼
  uploadCode: async (codeData: CodeUploadRequest) => {
    try {
      return await apiRequest.post('/cases/upload-code', codeData);
    } catch (error) {
      console.error('Upload code error:', error);
      throw error;
    }
  },

  // 獲取代碼下載鏈接
  getCodeDownloadUrl: async (caseId: number): Promise<string> => {
    try {
      const response = await apiRequest.get<{ codeUrl: string }>(`/cases/download-code/${caseId}`);
      return response.data?.codeUrl || '';
    } catch (error) {
      console.error('Get code download url error:', error);
      throw error;
    }
  },

  // 確認代碼
  confirmCode: async (userId: number, caseId: number, isApproved: boolean) => {
    try {
      return await apiRequest.post('/cases/confirm-code', {
        userId,
        caseId,
        isApproved
      });
    } catch (error) {
      console.error('Confirm code error:', error);
      throw error;
    }
  },

  // 獲取案件狀態
  getCaseStatus: async (caseId: number) => {
    try {
      const response = await apiRequest.get(`/cases/status/${caseId}`);
      return response.data;
    } catch (error) {
      console.error('Get case status error:', error);
      throw error;
    }
  },

  // 支付處理
  makePayment: async (paymentData: PaymentRequest): Promise<{ transactionId: string }> => {
    try {
      const response = await apiRequest.post<{ transactionId: string }>('/payment', paymentData);
      return response.data!;
    } catch (error) {
      console.error('Make payment error:', error);
      throw error;
    }
  },

  // 獲取交易記錄
  getTransactionHistory: async (userId: number): Promise<Transaction[]> => {
    try {
      const response = await apiRequest.get<Transaction[]>(`/users/transactions?userId=${userId}`);
      return response.data || [];
    } catch (error) {
      console.error('Get transaction history error:', error);
      throw error;
    }
  },

  // 提交評價
  submitEvaluation: async (evaluationData: EvaluationRequest) => {
    try {
      return await apiRequest.post('/evaluation', evaluationData);
    } catch (error) {
      console.error('Submit evaluation error:', error);
      throw error;
    }
  },

  // 獲取評價列表
  getEvaluations: async (userId: number): Promise<Evaluation[]> => {
    try {
      const response = await apiRequest.get<Evaluation[]>(`/evaluations/user/${userId}`);
      return response.data || [];
    } catch (error) {
      console.error('Get evaluations error:', error);
      throw error;
    }
  },

  // 更新案件狀態
  updateCaseStatus: async (caseId: number, status: Case['status']) => {
    try {
      return await apiRequest.put(`/cases/${caseId}/status`, { status });
    } catch (error) {
      console.error('Update case status error:', error);
      throw error;
    }
  }
}; 