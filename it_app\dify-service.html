<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI速應 - 服務詳情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://unpkg.com/lucide-static/font/lucide.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f7fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            padding-bottom: 20px;
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }
        .header {
            position: relative;
            overflow: hidden;
            border-radius: 0 0 16px 16px;
        }
        .back-button {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 36px;
            height: 36px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .share-button {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 36px;
            height: 36px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .image-container {
            position: relative;
            overflow: hidden;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .image-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }
        .service-image {
            width: 100%;
            height: auto;
            object-fit: cover;
            transition: transform 0.5s ease;
            transform-origin: center;
        }
        .service-image:hover {
            transform: scale(1.03);
        }
        .image-skeleton {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, #f0f0f0 25%, #f8f8f8 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            z-index: 1;
        }
        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }
        .image-loaded .image-skeleton {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        .tag {
            background: rgba(99, 102, 241, 0.1);
            color: #6366f1;
            border-radius: 6px;
            padding: 2px 8px;
            font-size: 12px;
            font-weight: 500;
        }
        .tab {
            padding: 10px 0;
            font-size: 14px;
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            color: #6366f1;
            border-bottom: 2px solid #6366f1;
            font-weight: 500;
        }
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .feature-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: rgba(99, 102, 241, 0.1);
            color: #6366f1;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
        }
        .bottom-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 100;
        }
    </style>
    <script>
        // 图片加载优化脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 预加载图片
            const preloadImages = () => {
                const images = document.querySelectorAll('img[loading="lazy"]');
                if ('IntersectionObserver' in window) {
                    const imageObserver = new IntersectionObserver((entries, observer) => {
                        entries.forEach(entry => {
                            if (entry.isIntersecting) {
                                const image = entry.target;
                                image.src = image.src; // 触发加载
                                imageObserver.unobserve(image);
                            }
                        });
                    });
                    
                    images.forEach(img => imageObserver.observe(img));
                }
            };
            
            // 图片加载完成后移除骨架屏
            const handleImageLoad = () => {
                const images = document.querySelectorAll('img[loading="lazy"]');
                images.forEach(img => {
                    if (img.complete) {
                        img.parentNode.classList.add('image-loaded');
                    } else {
                        img.onload = function() {
                            this.parentNode.classList.add('image-loaded');
                        };
                    }
                });
            };
            
            preloadImages();
            handleImageLoad();
        });
    </script>
</head>
<body>
    <!-- 顶部图片 -->
    <div class="header">
        <div class="back-button">
            <i class="lucide lucide-chevron-left"></i>
        </div>
        <div class="share-button">
            <i class="lucide lucide-share-2"></i>
        </div>
        <div class="relative w-full h-56 overflow-hidden">
            <div class="image-skeleton"></div>
            <img src="./images/dify.png" alt="Dify AI" class="w-full h-56 object-cover service-image" loading="lazy" onload="this.parentNode.classList.add('image-loaded')">
        </div>
    </div>

    <!-- 服务信息 -->
    <div class="p-4">
        <div class="flex justify-between items-start mb-2">
            <h1 class="text-xl font-bold">Dify AI 定制服務</h1>
            <div class="flex items-center text-yellow-500 text-sm">
                <i class="lucide lucide-star-filled"></i>
                <span class="ml-1">4.9 (328)</span>
            </div>
        </div>
        <div class="flex space-x-2 mb-4">
            <span class="tag">企業級</span>
            <span class="tag">熱門</span>
            <span class="tag">定制化</span>
        </div>
        <div class="flex justify-between items-center mb-6">
            <div>
                <span class="text-2xl font-bold text-indigo-600">¥2999</span>
                <span class="text-gray-500 line-through ml-2">¥3999</span>
            </div>
            <div class="text-sm text-gray-500">已售 328 | 好評率 98%</div>
        </div>

        <!-- 服务标签 -->
        <div class="flex justify-between border-b mb-4">
            <div class="tab active text-center flex-1">服務詳情</div>
            <div class="tab text-gray-600 text-center flex-1">客戶評價</div>
            <div class="tab text-gray-600 text-center flex-1">常見問題</div>
        </div>

        <!-- 服务详情 -->
        <div>
            <div class="glass-card p-4 mb-4">
                <h3 class="font-semibold mb-3">Dify AI 定制服務</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    專業定制企業AI助手，包含知識庫搭建與API集成
                </p>
            </div>

            <div class="glass-card p-4 mb-4">
                <h3 class="font-semibold mb-3">服務內容</h3>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="lucide lucide-file-search"></i>
                    </div>
                    <div>
                        <h4 class="font-medium">需求分析与规划</h4>
                        <p class="text-gray-600 text-sm">深入了解业务需求，制定AI助手功能规划</p>
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="lucide lucide-database"></i>
                    </div>
                    <div>
                        <h4 class="font-medium">知识库搭建</h4>
                        <p class="text-gray-600 text-sm">构建专业领域知识库，支持文档导入与管理</p>
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="lucide lucide-settings"></i>
                    </div>
                    <div>
                        <h4 class="font-medium">模型训练与优化</h4>
                        <p class="text-gray-600 text-sm">基于业务数据进行模型训练，提升回答准确性</p>
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="lucide lucide-plug"></i>
                    </div>
                    <div>
                        <h4 class="font-medium">API集成与部署</h4>
                        <p class="text-gray-600 text-sm">与现有系统无缝集成，提供多平台部署方案</p>
                    </div>
                </div>
            </div>

            <div class="glass-card p-4 mb-4">
                <h3 class="font-semibold mb-3">Dify平台优势</h3>
                <div class="flex flex-col items-center mb-4">
                    <div class="bg-white p-4 rounded-lg mb-3 image-container">
                        <div class="image-skeleton"></div>
                        <img src="./images/dify.png" alt="Dify Logo" class="w-24 h-24 service-image" loading="lazy" onload="this.parentNode.classList.add('image-loaded')">
                    </div>
                    <div class="w-full flex justify-center my-3">
                        <img src="./images/dify.png" alt="Define & Modify - Do It For You" class="w-full max-w-xs">
                    </div>
                </div>
                <p class="text-gray-600 text-sm leading-relaxed mb-3">
                    我们的服务基于Dify AI平台构建，Dify是一个强大的LLM应用开发平台，让您能够轻松定义和修改AI应用。通过Dify的可视化界面，您可以快速构建、部署和管理AI应用，无需复杂的编程知识。
                </p>
                <div class="flex flex-wrap gap-2 mt-2">
                    <span class="tag bg-blue-50 text-blue-600">开源</span>
                    <span class="tag bg-green-50 text-green-600">易用</span>
                    <span class="tag bg-purple-50 text-purple-600">强大</span>
                    <span class="tag bg-orange-50 text-orange-600">可扩展</span>
                </div>
            </div>

            <div class="glass-card p-4 mb-4">
                <h3 class="font-semibold mb-3">適用場景</h3>
                <div class="grid grid-cols-2 gap-3">
                    <div class="bg-indigo-50 p-3 rounded-lg">
                        <div class="flex items-center mb-2">
                            <i class="lucide lucide-message-circle text-indigo-600 mr-2"></i>
                            <span class="font-medium">智能客服</span>
                        </div>
                        <p class="text-gray-600 text-xs">7×24小时自动回复客户咨询</p>
                    </div>
                    <div class="bg-indigo-50 p-3 rounded-lg">
                        <div class="flex items-center mb-2">
                            <i class="lucide lucide-users text-indigo-600 mr-2"></i>
                            <span class="font-medium">员工助手</span>
                        </div>
                        <p class="text-gray-600 text-xs">快速解答内部员工业务问题</p>
                    </div>
                    <div class="bg-indigo-50 p-3 rounded-lg">
                        <div class="flex items-center mb-2">
                            <i class="lucide lucide-book-open text-indigo-600 mr-2"></i>
                            <span class="font-medium">知识管理</span>
                        </div>
                        <p class="text-gray-600 text-xs">企业知识智能检索与管理</p>
                    </div>
                    <div class="bg-indigo-50 p-3 rounded-lg">
                        <div class="flex items-center mb-2">
                            <i class="lucide lucide-shopping-cart text-indigo-600 mr-2"></i>
                            <span class="font-medium">销售顾问</span>
                        </div>
                        <p class="text-gray-600 text-xs">智能产品推荐与销售支持</p>
                    </div>
                </div>
            </div>

            <div class="glass-card p-4 mb-4">
                <h3 class="font-semibold mb-3">服務流程</h3>
                <div class="relative pl-8 pb-1">
                    <div class="absolute left-3 top-0 bottom-0 w-0.5 bg-indigo-100"></div>
                    <div class="absolute left-0 top-0 w-6 h-6 rounded-full bg-indigo-600 flex items-center justify-center text-white text-xs">1</div>
                    <div class="mb-4">
                        <h4 class="font-medium">需求沟通</h4>
                        <p class="text-gray-600 text-sm">深入了解您的业务需求和目标</p>
                    </div>
                </div>
                <div class="relative pl-8 pb-1">
                    <div class="absolute left-3 top-0 bottom-0 w-0.5 bg-indigo-100"></div>
                    <div class="absolute left-0 top-0 w-6 h-6 rounded-full bg-indigo-600 flex items-center justify-center text-white text-xs">2</div>
                    <div class="mb-4">
                        <h4 class="font-medium">方案设计</h4>
                        <p class="text-gray-600 text-sm">制定个性化AI助手解决方案</p>
                    </div>
                </div>
                <div class="relative pl-8 pb-1">
                    <div class="absolute left-3 top-0 bottom-0 w-0.5 bg-indigo-100"></div>
                    <div class="absolute left-0 top-0 w-6 h-6 rounded-full bg-indigo-600 flex items-center justify-center text-white text-xs">3</div>
                    <div class="mb-4">
                        <h4 class="font-medium">开发实施</h4>
                        <p class="text-gray-600 text-sm">知识库搭建、模型训练与系统集成</p>
                    </div>
                </div>
                <div class="relative pl-8">
                    <div class="absolute left-0 top-0 w-6 h-6 rounded-full bg-indigo-600 flex items-center justify-center text-white text-xs">4</div>
                    <div class="mb-1">
                        <h4 class="font-medium">交付使用</h4>
                        <p class="text-gray-600 text-sm">系统部署、培训与后续支持</p>
                    </div>
                </div>
            </div>

            <!-- 成功案例 -->
            <div class="glass-card p-4 mb-20">
                <h3 class="font-semibold mb-3">成功案例</h3>
                <div class="mb-4 pb-4 border-b border-gray-100">
                    <div class="flex items-center mb-2">
                        <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?q=80&w=50&auto=format&fit=crop" class="w-8 h-8 rounded-full mr-2" alt="客户头像">
                        <h4 class="font-medium">科技创新有限公司</h4>
                    </div>
                    <p class="text-gray-600 text-sm mb-2">通过Dify AI定制服务，构建了专业的客服AI助手，接入率提升40%，客户满意度大幅提升。</p>
                    <div class="flex items-center text-yellow-500 text-xs">
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-filled"></i>
                    </div>
                </div>
                <div>
                    <div class="flex items-center mb-2">
                        <img src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?q=80&w=50&auto=format&fit=crop" class="w-8 h-8 rounded-full mr-2" alt="客户头像">
                        <h4 class="font-medium">金融服务集团</h4>
                    </div>
                    <p class="text-gray-600 text-sm mb-2">利用Dify AI构建了内部知识库助手，员工培训时间缩短50%，工作效率显著提升。</p>
                    <div class="flex items-center text-yellow-500 text-xs">
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-half"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部购买栏 -->
    <div class="bottom-bar">
        <div class="flex items-center">
            <div class="mr-4 text-center">
                <i class="lucide lucide-message-circle text-gray-600"></i>
                <div class="text-xs text-gray-600">咨询</div>
            </div>
            <div class="text-center">
                <i class="lucide lucide-heart text-gray-600"></i>
                <div class="text-xs text-gray-600">收藏</div>
            </div>
        </div>
        <div class="flex">
            <button class="px-4 py-2 bg-indigo-100 text-indigo-600 rounded-l-full font-medium">免费咨询</button>
            <button class="px-4 py-2 bg-indigo-600 text-white rounded-r-full font-medium">立即購買</button>
        </div>
    </div>
</body>
</html>