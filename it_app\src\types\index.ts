// 用戶相關類型
export interface User {
  id: number;
  username: string;
  contact: string;
  role: 'user' | 'contractor';
  nickname?: string;
  avatar?: string;
  isVerified: boolean;
  createdAt: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  password: string;
  contact: string;
  verificationCode: string;
  authType: 'sms' | 'email';
}

export interface AuthResponse {
  code: number;
  message: string;
  data: {
    userId: number;
    token: string;
  };
}

// 案件相關類型
export interface Case {
  id: number;
  userId: number;
  caseType: string;
  description: string;
  expectedResult: string;
  category: string;
  status: '待處理' | '處理中' | '已完成' | '已取消';
  createdAt: string;
  updatedAt: string;
}

export interface CaseSubmitRequest {
  userId: number;
  caseType: string;
  description: string;
  expectedResult: string;
}

export interface CaseResponse {
  id: number;
  userId: number;
  caseId: number;
  responseTime: string;
}

// 消息相關類型
export interface Message {
  id: number;
  senderId: number;
  receiverId: number;
  caseId: number;
  content: string;
  messageType: 'text' | 'image' | 'file';
  sendTime: string;
}

export interface SendMessageRequest {
  senderId: number;
  receiverId: number;
  caseId: number;
  content: string;
  messageType: 'text' | 'image' | 'file';
}

// 代碼相關類型
export interface CodeSubmission {
  id: number;
  userId: number;
  caseId: number;
  codeUrl: string;
  uploadTime: string;
}

export interface CodeUploadRequest {
  userId: number;
  caseId: number;
  codeUrl: string;
}

// 支付相關類型
export interface Transaction {
  id: number;
  userId: number;
  caseId: number;
  amount: number;
  paymentMethod: string;
  transactionId: string;
  paymentTime: string;
}

export interface PaymentRequest {
  userId: number;
  caseId: number;
  paymentMethod: string;
  amount: number;
}

// 評價相關類型
export interface Evaluation {
  id: number;
  userId: number;
  receiverId: number;
  caseId: number;
  rating: number;
  comment: string;
  createdAt: string;
}

export interface EvaluationRequest {
  userId: number;
  receiverId: number;
  caseId: number;
  rating: number;
  comment: string;
}

// API 響應類型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data?: T;
}

// 通用類型
export interface UserProfile {
  nickname?: string;
  avatar?: string;
  contact: string;
  enableGesture: boolean;
  enableFingerprint: boolean;
  receiveCaseNotice: boolean;
  receiveMessageReminder: boolean;
  themeColor?: string;
  fontSize?: string;
}

export interface NotificationSettings {
  receiveCaseNotice: boolean;
  receiveMessageReminder: boolean;
}

export interface SecuritySettings {
  password: string;
  enableGesture: boolean;
  enableFingerprint: boolean;
}

export interface CustomizationSettings {
  themeColor: string;
  fontSize: string;
} 