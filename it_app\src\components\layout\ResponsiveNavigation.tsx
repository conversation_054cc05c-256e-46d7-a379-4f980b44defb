import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Home,
  Briefcase,
  Award,
  Clock,
  User,
  LogIn,
  Menu,
  X
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface NavItem {
  path: string;
  icon: React.ComponentType<any>;
  label: string;
  activeColor: string;
  requireAuth?: boolean;
}

interface ResponsiveNavigationProps {
  className?: string;
}

const ResponsiveNavigation: React.FC<ResponsiveNavigationProps> = ({ className = '' }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { state } = useAuth();
  const [isMobile, setIsMobile] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);

  // 檢測設備類型
  useEffect(() => {
    const checkDevice = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkDevice();
    window.addEventListener('resize', checkDevice);
    return () => window.removeEventListener('resize', checkDevice);
  }, []);

  // 導航項目配置
  const getNavItems = (): NavItem[] => {
    const baseItems: NavItem[] = [
      {
        path: '/',
        icon: Home,
        label: '首頁',
        activeColor: 'text-primary-600'
      },
      {
        path: '/services',
        icon: Briefcase,
        label: '服務',
        activeColor: 'text-primary-600'
      },
      {
        path: '/cases',
        icon: Award,
        label: '案例',
        activeColor: 'text-primary-600'
      }
    ];

    if (state.isAuthenticated) {
      // 已登入用戶顯示完整導航
      return [
        ...baseItems,
        {
          path: '/orders',
          icon: Clock,
          label: '訂單',
          activeColor: 'text-primary-600',
          requireAuth: true
        },
        {
          path: '/profile',
          icon: User,
          label: '我的',
          activeColor: 'text-primary-600',
          requireAuth: true
        }
      ];
    } else {
      // 未登入用戶顯示登入按鈕
      return [
        ...baseItems,
        {
          path: '/orders',
          icon: Clock,
          label: '訂單',
          activeColor: 'text-primary-600'
        },
        {
          path: '/login',
          icon: LogIn,
          label: '登入',
          activeColor: 'text-primary-600'
        }
      ];
    }
  };

  const navItems = getNavItems();

  const handleNavClick = (item: NavItem) => {
    navigate(item.path);
    setShowMobileMenu(false);
  };

  const isActive = (path: string) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  // 移動端底部導航
  if (isMobile) {
    return (
      <nav className={`fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-40 ${className}`}>
        <div className="grid grid-cols-5 h-16">
          {navItems.map((item) => {
            const IconComponent = item.icon;
            const active = isActive(item.path);
            
            return (
              <button
                key={item.path}
                onClick={() => handleNavClick(item)}
                className={`flex flex-col items-center justify-center space-y-1 transition-colors ${
                  active 
                    ? item.activeColor 
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <IconComponent className="w-5 h-5" />
                <span className="text-xs font-medium">{item.label}</span>
              </button>
            );
          })}
        </div>
      </nav>
    );
  }

  // 桌面端頂部導航
  return (
    <>
      {/* 桌面端導航 */}
      <nav className={`bg-white border-b border-gray-200 ${className}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex items-center">
              <button
                onClick={() => navigate('/')}
                className="text-xl font-bold text-primary-600 hover:text-primary-700 transition-colors"
              >
                AI速應
              </button>
            </div>

            {/* 桌面端導航項目 */}
            <div className="hidden md:flex items-center space-x-8">
              {navItems.map((item) => {
                const IconComponent = item.icon;
                const active = isActive(item.path);
                
                return (
                  <button
                    key={item.path}
                    onClick={() => handleNavClick(item)}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      active
                        ? 'bg-primary-100 text-primary-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    <IconComponent className="w-4 h-4" />
                    <span>{item.label}</span>
                  </button>
                );
              })}
            </div>

            {/* 移動端菜單按鈕 */}
            <div className="md:hidden">
              <button
                onClick={() => setShowMobileMenu(!showMobileMenu)}
                className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors"
              >
                {showMobileMenu ? (
                  <X className="w-6 h-6" />
                ) : (
                  <Menu className="w-6 h-6" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* 移動端下拉菜單 */}
        {showMobileMenu && (
          <div className="md:hidden border-t border-gray-200 bg-white">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navItems.map((item) => {
                const IconComponent = item.icon;
                const active = isActive(item.path);
                
                return (
                  <button
                    key={item.path}
                    onClick={() => handleNavClick(item)}
                    className={`flex items-center space-x-3 w-full px-3 py-2 rounded-md text-base font-medium transition-colors ${
                      active
                        ? 'bg-primary-100 text-primary-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    <IconComponent className="w-5 h-5" />
                    <span>{item.label}</span>
                  </button>
                );
              })}
            </div>
          </div>
        )}
      </nav>
    </>
  );
};

export default ResponsiveNavigation;
