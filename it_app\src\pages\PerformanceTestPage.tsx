import React, { useState, useEffect } from 'react';
import { Play, Download, RefreshCw, CheckCircle, XCircle, Clock, Zap, Monitor, Search } from 'lucide-react';
import ResponsiveLayout from '@/components/layout/ResponsiveLayout';
import { performanceTest, PerformanceTestResult } from '@/utils/performanceTest';
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor';

const PerformanceTestPage: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<PerformanceTestResult[]>([]);
  const [currentTest, setCurrentTest] = useState<string>('');
  const { metrics, settings, deviceCapability } = usePerformanceMonitor();

  const runTests = async () => {
    setIsRunning(true);
    setResults([]);
    setCurrentTest('正在初始化測試...');

    try {
      const testResults = await performanceTest.runAllTests();
      setResults(testResults);
      setCurrentTest('測試完成');
    } catch (error) {
      console.error('測試失敗:', error);
      setCurrentTest('測試失敗');
    } finally {
      setIsRunning(false);
    }
  };

  const downloadReport = () => {
    const report = performanceTest.generateReport();
    const blob = new Blob([report], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `performance-report-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getTestIcon = (testName: string) => {
    if (testName.includes('載入')) return Clock;
    if (testName.includes('3D') || testName.includes('渲染')) return Zap;
    if (testName.includes('響應式')) return Monitor;
    if (testName.includes('SEO')) return Search;
    return CheckCircle;
  };

  const passedTests = results.filter(r => r.passed).length;
  const totalTests = results.length;
  const passRate = totalTests > 0 ? (passedTests / totalTests * 100).toFixed(1) : '0';

  return (
    <ResponsiveLayout>
      <div className="max-w-4xl mx-auto p-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">性能測試中心</h1>
          <p className="text-gray-600">
            測試應用的性能表現，包括載入速度、3D 渲染、響應式設計和 SEO 優化
          </p>
        </div>

        {/* 當前性能指標 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-white rounded-lg p-4 shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">當前 FPS</p>
                <p className="text-2xl font-bold text-gray-900">{metrics.fps.toFixed(0)}</p>
              </div>
              <Zap className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg p-4 shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">內存使用</p>
                <p className="text-2xl font-bold text-gray-900">{metrics.memoryUsage.toFixed(0)}MB</p>
              </div>
              <Monitor className="w-8 h-8 text-green-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg p-4 shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">載入時間</p>
                <p className="text-2xl font-bold text-gray-900">{(metrics.loadTime / 1000).toFixed(1)}s</p>
              </div>
              <Clock className="w-8 h-8 text-orange-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg p-4 shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">設備等級</p>
                <p className="text-2xl font-bold text-gray-900">
                  {deviceCapability?.isHighEnd ? '高端' : deviceCapability?.isMobile ? '移動' : '中端'}
                </p>
              </div>
              <Monitor className="w-8 h-8 text-purple-500" />
            </div>
          </div>
        </div>

        {/* 測試控制 */}
        <div className="bg-white rounded-lg p-6 shadow-sm border mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">性能測試</h2>
            <div className="flex space-x-3">
              <button
                onClick={runTests}
                disabled={isRunning}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isRunning ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Play className="w-4 h-4 mr-2" />
                )}
                {isRunning ? '測試中...' : '開始測試'}
              </button>

              {results.length > 0 && (
                <button
                  onClick={downloadReport}
                  className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  <Download className="w-4 h-4 mr-2" />
                  下載報告
                </button>
              )}
            </div>
          </div>

          {isRunning && (
            <div className="mb-4">
              <div className="flex items-center text-blue-600">
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                <span>{currentTest}</span>
              </div>
            </div>
          )}

          {results.length > 0 && (
            <div className="mb-4">
              <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                <span>測試通過率</span>
                <span>{passedTests}/{totalTests} ({passRate}%)</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${passRate}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>

        {/* 測試結果 */}
        {results.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-6 border-b">
              <h2 className="text-xl font-semibold text-gray-900">測試結果</h2>
            </div>
            <div className="divide-y">
              {results.map((result, index) => {
                const TestIcon = getTestIcon(result.testName);
                return (
                  <div key={index} className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3">
                        <div className={`p-2 rounded-lg ${result.passed ? 'bg-green-100' : 'bg-red-100'}`}>
                          <TestIcon className={`w-5 h-5 ${result.passed ? 'text-green-600' : 'text-red-600'}`} />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900 mb-1">{result.testName}</h3>
                          <p className="text-sm text-gray-600">{result.details}</p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        {result.passed ? (
                          <CheckCircle className="w-6 h-6 text-green-600" />
                        ) : (
                          <XCircle className="w-6 h-6 text-red-600" />
                        )}
                      </div>
                    </div>

                    {/* 詳細指標 */}
                    <div className="mt-4 grid grid-cols-3 gap-4 text-sm">
                      {result.duration > 0 && (
                        <div>
                          <span className="text-gray-500">持續時間:</span>
                          <span className="ml-1 font-medium">{result.duration.toFixed(0)}ms</span>
                        </div>
                      )}
                      {result.fps > 0 && (
                        <div>
                          <span className="text-gray-500">FPS:</span>
                          <span className="ml-1 font-medium">{result.fps.toFixed(1)}</span>
                        </div>
                      )}
                      {result.memoryUsage > 0 && (
                        <div>
                          <span className="text-gray-500">內存:</span>
                          <span className="ml-1 font-medium">{result.memoryUsage.toFixed(1)}MB</span>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* 性能設置信息 */}
        {settings && (
          <div className="mt-8 bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">當前性能設置</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-500">粒子數量:</span>
                <span className="ml-1 font-medium">{settings.particleCount}</span>
              </div>
              <div>
                <span className="text-gray-500">動畫速度:</span>
                <span className="ml-1 font-medium">{settings.animationSpeed}</span>
              </div>
              <div>
                <span className="text-gray-500">渲染比例:</span>
                <span className="ml-1 font-medium">{settings.renderScale}</span>
              </div>
              <div>
                <span className="text-gray-500">質量等級:</span>
                <span className="ml-1 font-medium">{settings.quality}</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </ResponsiveLayout>
  );
};

export default PerformanceTestPage;
