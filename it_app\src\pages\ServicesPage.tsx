import React, { useState, Suspense } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, Filter, ArrowRight, MessageCircle } from 'lucide-react';
import { useMembership } from '@/hooks/useMembership';
import MobileLayout from '@/components/layout/MobileLayout';
import MembershipPrompt from '@/components/auth/MembershipPrompt';
import { View, Common } from '@/components/canvas/View';
import { Card3D } from '@/components/canvas/Card3D';
import { ParticleSystem } from '@/components/canvas/ParticleSystem';

// AI 服務數據
const aiServices = [
  {
    id: 1,
    title: 'RAG 檢索增強生成',
    description: '智能文檔問答系統',
    category: 'ai-search',
    price: '¥5,000 - ¥15,000',
    color: '#3b82f6'
  },
  {
    id: 2,
    title: 'Dify 智能平台',
    description: '低代碼AI應用開發',
    category: 'ai-platform',
    price: '¥8,000 - ¥25,000',
    color: '#10b981'
  },
  {
    id: 3,
    title: 'AI 視頻處理',
    description: '智能視頻分析與生成',
    category: 'ai-media',
    price: '¥10,000 - ¥30,000',
    color: '#f59e0b'
  },
  {
    id: 4,
    title: '智能客服機器人',
    description: '24/7 自動客戶服務',
    category: 'ai-chat',
    price: '¥3,000 - ¥12,000',
    color: '#ef4444'
  },
  {
    id: 5,
    title: '圖像識別系統',
    description: '物體檢測與分類',
    category: 'ai-vision',
    price: '¥6,000 - ¥20,000',
    color: '#8b5cf6'
  },
  {
    id: 6,
    title: '數據分析平台',
    description: '智能數據洞察',
    category: 'ai-analytics',
    price: '¥7,000 - ¥22,000',
    color: '#06b6d4'
  }
];

const categories = [
  { id: 'all', name: '全部服務' },
  { id: 'ai-search', name: '智能搜索' },
  { id: 'ai-platform', name: 'AI平台' },
  { id: 'ai-media', name: '媒體處理' },
  { id: 'ai-chat', name: '對話系統' },
  { id: 'ai-vision', name: '計算機視覺' },
  { id: 'ai-analytics', name: '數據分析' }
];

const ServicesPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const {
    isMember,
    showPrompt,
    promptFeature,
    promptDescription,
    requireMembership,
    closePrompt
  } = useMembership();

  const filteredServices = aiServices.filter(service => {
    const matchesSearch = service.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || service.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleServiceClick = (service: typeof aiServices[0]) => {
    navigate(`/services/${service.id}`);
  };

  const handleContactService = (service: typeof aiServices[0]) => {
    requireMembership(
      '聯繫服務商',
      '與服務提供商直接溝通，獲取詳細報價和方案',
      () => {
        // 執行聯繫邏輯
        console.log('聯繫服務商:', service.title);
        // TODO: 實現聯繫功能
      }
    );
  };

  return (
    <MobileLayout enable3D={true}>
      <div className="flex flex-col h-full bg-gray-50 relative">
        {/* 3D 背景粒子效果 */}
        <View className="absolute inset-0 opacity-20">
          <Suspense fallback={null}>
            <ParticleSystem 
              count={500}
              color="#3b82f6"
              size={0.003}
              speed={0.1}
              spread={8}
            />
            <Common />
          </Suspense>
        </View>

        {/* 頭部搜索區域 */}
        <div className="bg-white px-6 py-4 shadow-sm relative z-10">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">AI 服務市場</h1>
          
          {/* 搜索框 */}
          <div className="relative mb-4">
            <input
              type="text"
              placeholder="搜索 AI 服務..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-3 pl-12 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          </div>

          {/* 分類篩選 */}
          <div className="flex overflow-x-auto space-x-2 pb-2">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-colors ${
                  selectedCategory === category.id
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* 3D 服務卡片網格 */}
        <div className="flex-1 px-6 py-6 relative z-10">
          <div className="grid grid-cols-1 gap-6">
            {filteredServices.map((service, index) => (
              <div key={service.id} className="h-32 relative">
                <View className="w-full h-full">
                  <Suspense fallback={null}>
                    <Card3D
                      title={service.title}
                      description={service.description}
                      color="#ffffff"
                      hoverColor="#f0f9ff"
                      position={[0, 0, 0]}
                      onClick={() => handleServiceClick(service)}
                    />
                    <Common />
                  </Suspense>
                </View>
                
                {/* 2D 覆蓋信息 */}
                <div className="absolute bottom-2 left-4 right-4 flex justify-between items-center">
                  <div className="text-xs text-gray-600 pointer-events-none">
                    {service.price}
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleContactService(service);
                      }}
                      className="p-1 rounded-full bg-white/80 hover:bg-white transition-colors shadow-sm"
                    >
                      <MessageCircle className="w-4 h-4 text-primary-600" />
                    </button>
                    <ArrowRight className="w-4 h-4 text-primary-600 pointer-events-none" />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredServices.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-2">
                <Search className="w-12 h-12 mx-auto" />
              </div>
              <p className="text-gray-500">沒有找到匹配的服務</p>
            </div>
          )}
        </div>

        {/* 底部空白 */}
        <div className="h-4"></div>
      </div>

      {/* 會員提示 */}
      <MembershipPrompt
        isOpen={showPrompt}
        onClose={closePrompt}
        feature={promptFeature}
        description={promptDescription}
      />
    </MobileLayout>
  );
};

export default ServicesPage;
