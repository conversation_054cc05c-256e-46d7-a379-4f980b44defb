/**
 * 設備性能檢測和 3D 效果管理系統
 */

export interface DeviceCapability {
  isHighEnd: boolean;
  isMobile: boolean;
  supportsWebGL2: boolean;
  maxTextureSize: number;
  devicePixelRatio: number;
  hardwareConcurrency: number;
  memoryGB?: number;
}

export interface Performance3DSettings {
  particleCount: number;
  animationSpeed: number;
  enableShadows: boolean;
  renderScale: number;
  quality: 'low' | 'medium' | 'high';
  enablePostProcessing: boolean;
}

export class PerformanceManager {
  private static instance: PerformanceManager;
  private deviceCapability: DeviceCapability | null = null;
  private settings: Performance3DSettings | null = null;
  private performanceMetrics: {
    fps: number[];
    memoryUsage: number[];
    loadTime: number;
  } = {
    fps: [],
    memoryUsage: [],
    loadTime: 0
  };

  private constructor() {}

  static getInstance(): PerformanceManager {
    if (!PerformanceManager.instance) {
      PerformanceManager.instance = new PerformanceManager();
    }
    return PerformanceManager.instance;
  }

  /**
   * 檢測設備性能能力
   */
  detectDeviceCapability(): DeviceCapability {
    if (this.deviceCapability) {
      return this.deviceCapability;
    }

    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    const gl2 = canvas.getContext('webgl2');

    // 基礎檢測
    const isMobile = /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const devicePixelRatio = window.devicePixelRatio || 1;
    const hardwareConcurrency = navigator.hardwareConcurrency || 4;

    // WebGL 能力檢測
    let maxTextureSize = 2048;
    let isHighEnd = false;

    if (gl) {
      maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE);
      const renderer = gl.getParameter(gl.RENDERER);
      const vendor = gl.getParameter(gl.VENDOR);
      
      // 根據 GPU 信息判斷性能等級
      isHighEnd = this.isHighEndGPU(renderer, vendor) && maxTextureSize >= 4096;
    }

    // 內存檢測（如果支持）
    let memoryGB: number | undefined;
    if ('memory' in performance && (performance as any).memory) {
      const memory = (performance as any).memory;
      memoryGB = memory.jsHeapSizeLimit / (1024 * 1024 * 1024);
    }

    this.deviceCapability = {
      isHighEnd: isHighEnd && !isMobile && hardwareConcurrency >= 4,
      isMobile,
      supportsWebGL2: !!gl2,
      maxTextureSize,
      devicePixelRatio,
      hardwareConcurrency,
      memoryGB
    };

    return this.deviceCapability;
  }

  /**
   * 根據設備能力調整 3D 效果設置
   */
  getOptimal3DSettings(): Performance3DSettings {
    if (this.settings) {
      return this.settings;
    }

    const capability = this.detectDeviceCapability();
    
    if (capability.isMobile) {
      // 移動設備：低質量設置
      this.settings = {
        particleCount: 30,
        animationSpeed: 0.008,
        enableShadows: false,
        renderScale: 0.7,
        quality: 'low',
        enablePostProcessing: false
      };
    } else if (capability.isHighEnd) {
      // 高端設備：高質量設置
      this.settings = {
        particleCount: 200,
        animationSpeed: 0.02,
        enableShadows: true,
        renderScale: 1.0,
        quality: 'high',
        enablePostProcessing: true
      };
    } else {
      // 中端設備：中等質量設置
      this.settings = {
        particleCount: 100,
        animationSpeed: 0.015,
        enableShadows: false,
        renderScale: 0.85,
        quality: 'medium',
        enablePostProcessing: false
      };
    }

    return this.settings;
  }

  /**
   * 動態調整性能設置
   */
  adjustPerformanceBasedOnMetrics(): void {
    const avgFPS = this.getAverageFPS();
    const avgMemory = this.getAverageMemoryUsage();

    if (!this.settings) {
      this.getOptimal3DSettings();
      return;
    }

    // 如果 FPS 過低，降低質量
    if (avgFPS < 25 && avgFPS > 0) {
      this.settings.particleCount = Math.max(20, this.settings.particleCount * 0.8);
      this.settings.renderScale = Math.max(0.5, this.settings.renderScale * 0.9);
      this.settings.enableShadows = false;
      this.settings.enablePostProcessing = false;
      console.log('Performance adjusted: Reduced quality due to low FPS');
    }

    // 如果內存使用過高，減少粒子數量
    if (avgMemory > 100) { // 100MB
      this.settings.particleCount = Math.max(10, this.settings.particleCount * 0.7);
      console.log('Performance adjusted: Reduced particles due to high memory usage');
    }
  }

  /**
   * 記錄 FPS
   */
  recordFPS(fps: number): void {
    this.performanceMetrics.fps.push(fps);
    // 只保留最近 60 個記錄（約 1 分鐘）
    if (this.performanceMetrics.fps.length > 60) {
      this.performanceMetrics.fps.shift();
    }

    // 每 10 次記錄檢查一次性能
    if (this.performanceMetrics.fps.length % 10 === 0) {
      this.adjustPerformanceBasedOnMetrics();
    }
  }

  /**
   * 記錄內存使用
   */
  recordMemoryUsage(): void {
    if ('memory' in performance && (performance as any).memory) {
      const memory = (performance as any).memory;
      const usedMB = memory.usedJSHeapSize / (1024 * 1024);
      this.performanceMetrics.memoryUsage.push(usedMB);
      
      // 只保留最近 30 個記錄
      if (this.performanceMetrics.memoryUsage.length > 30) {
        this.performanceMetrics.memoryUsage.shift();
      }
    }
  }

  /**
   * 記錄頁面載入時間
   */
  recordLoadTime(): void {
    if (performance.timing) {
      this.performanceMetrics.loadTime = 
        performance.timing.loadEventEnd - performance.timing.navigationStart;
    }
  }

  /**
   * 獲取平均 FPS
   */
  getAverageFPS(): number {
    if (this.performanceMetrics.fps.length === 0) return 0;
    return this.performanceMetrics.fps.reduce((a, b) => a + b, 0) / this.performanceMetrics.fps.length;
  }

  /**
   * 獲取平均內存使用
   */
  getAverageMemoryUsage(): number {
    if (this.performanceMetrics.memoryUsage.length === 0) return 0;
    return this.performanceMetrics.memoryUsage.reduce((a, b) => a + b, 0) / this.performanceMetrics.memoryUsage.length;
  }

  /**
   * 獲取性能報告
   */
  getPerformanceReport() {
    return {
      deviceCapability: this.deviceCapability,
      settings: this.settings,
      metrics: {
        averageFPS: this.getAverageFPS(),
        averageMemoryUsage: this.getAverageMemoryUsage(),
        loadTime: this.performanceMetrics.loadTime
      }
    };
  }

  /**
   * 判斷是否為高端 GPU
   */
  private isHighEndGPU(renderer: string, vendor: string): boolean {
    const highEndKeywords = [
      'RTX', 'GTX 1060', 'GTX 1070', 'GTX 1080', 'GTX 1650', 'GTX 1660',
      'RX 580', 'RX 590', 'RX 6600', 'RX 6700', 'RX 6800',
      'Apple M1', 'Apple M2', 'Apple M3',
      'Adreno 640', 'Adreno 650', 'Adreno 660', 'Adreno 730',
      'Mali-G78', 'Mali-G710', 'Mali-G715'
    ];

    const rendererUpper = renderer.toUpperCase();
    const vendorUpper = vendor.toUpperCase();
    
    return highEndKeywords.some(keyword => 
      rendererUpper.includes(keyword.toUpperCase()) || 
      vendorUpper.includes(keyword.toUpperCase())
    );
  }
}

// 導出單例實例
export const performanceManager = PerformanceManager.getInstance();
