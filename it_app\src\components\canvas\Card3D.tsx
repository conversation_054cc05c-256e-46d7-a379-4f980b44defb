'use client'

import { useRef, useState } from 'react'
import { useFrame } from '@react-three/fiber'
import { RoundedBox, Text } from '@react-three/drei'
import * as THREE from 'three'

interface Card3DProps {
  title: string
  description?: string
  color?: string
  position?: [number, number, number]
  onClick?: () => void
  hoverColor?: string
}

export function Card3D({ 
  title,
  description,
  color = '#ffffff',
  position = [0, 0, 0],
  onClick,
  hoverColor = '#f0f9ff'
}: Card3DProps) {
  const meshRef = useRef<THREE.Mesh>(null)
  const [hovered, setHovered] = useState(false)
  const [clicked, setClicked] = useState(false)

  useFrame((state) => {
    if (meshRef.current) {
      // 懸浮動畫
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.1
      
      // 懸停時的旋轉效果
      if (hovered) {
        meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 3) * 0.1
      }
    }
  })

  return (
    <group 
      position={position}
      onClick={(e) => {
        e.stopPropagation()
        setClicked(!clicked)
        onClick?.()
      }}
      onPointerOver={(e) => {
        e.stopPropagation()
        setHovered(true)
        document.body.style.cursor = 'pointer'
      }}
      onPointerOut={(e) => {
        e.stopPropagation()
        setHovered(false)
        document.body.style.cursor = 'auto'
      }}
    >
      {/* 卡片主體 */}
      <RoundedBox
        ref={meshRef}
        args={[2, 1.2, 0.1]}
        radius={0.1}
        smoothness={4}
        scale={clicked ? 1.1 : hovered ? 1.05 : 1}
      >
        <meshStandardMaterial
          color={hovered ? hoverColor : color}
          transparent
          opacity={0.9}
          roughness={0.1}
          metalness={0.1}
        />
      </RoundedBox>

      {/* 標題文字 */}
      <Text
        position={[0, 0.2, 0.06]}
        fontSize={0.15}
        color="#1f2937"
        anchorX="center"
        anchorY="middle"
        font="/fonts/Inter-Bold.woff"
        maxWidth={1.8}
      >
        {title}
      </Text>

      {/* 描述文字 */}
      {description && (
        <Text
          position={[0, -0.1, 0.06]}
          fontSize={0.08}
          color="#6b7280"
          anchorX="center"
          anchorY="middle"
          font="/fonts/Inter-Regular.woff"
          maxWidth={1.6}
        >
          {description}
        </Text>
      )}

      {/* 發光效果 */}
      {hovered && (
        <pointLight
          position={[0, 0, 0.5]}
          intensity={0.5}
          color="#3b82f6"
          distance={3}
        />
      )}
    </group>
  )
}
