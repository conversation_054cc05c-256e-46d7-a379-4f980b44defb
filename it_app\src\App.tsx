import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from '@/contexts/AuthContext';

// 頁面組件
import HomePage from '@/pages/HomePage';
import LoginPage from '@/pages/LoginPage';
import EmailLoginPage from '@/pages/EmailLoginPage';
import ServicesPage from '@/pages/ServicesPage';
import OrdersPage from '@/pages/OrdersPage';
import ProfilePage from '@/pages/ProfilePage';
import CasesPage from '@/pages/CasesPage';
import CaseDetailPage from '@/pages/CaseDetailPage';
// 暫時創建占位符組件，後續會實現
const RegisterPage = () => <div>註冊頁面 - 開發中</div>;
const MessagesPage = () => <div>消息頁面 - 開發中</div>;

// 登入頁面路由守護組件（避免已登入用戶重複訪問登入頁）
const LoginRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const token = localStorage.getItem('authToken');
  // 如果已登入，重定向到首頁，否則顯示登入頁面
  return !token ? <>{children}</> : <Navigate to="/" replace />;
};

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Routes>
            {/* 登入相關路由 */}
            <Route
              path="/login"
              element={
                <LoginRoute>
                  <LoginPage />
                </LoginRoute>
              }
            />
            <Route
              path="/email-login"
              element={
                <LoginRoute>
                  <EmailLoginPage />
                </LoginRoute>
              }
            />
            <Route
              path="/register"
              element={
                <LoginRoute>
                  <RegisterPage />
                </LoginRoute>
              }
            />

            {/* 公開路由 - 所有用戶都可以訪問 */}
            <Route path="/" element={<HomePage />} />
            <Route path="/services" element={<ServicesPage />} />
            <Route path="/cases" element={<CasesPage />} />
            <Route path="/cases/:id" element={<CaseDetailPage />} />

            {/* 會員功能路由 - 需要登入但不強制重定向 */}
            <Route path="/orders" element={<OrdersPage />} />
            <Route path="/messages" element={<MessagesPage />} />
            <Route path="/profile" element={<ProfilePage />} />

            {/* 默認重定向 */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App; 