<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI速應 - AI视频生成与定制服务</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://unpkg.com/lucide-static/font/lucide.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f7fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            padding-bottom: 20px;
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }
        .header {
            position: relative;
        }
        .back-button {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 36px;
            height: 36px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .share-button {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 36px;
            height: 36px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .tag {
            background: rgba(99, 102, 241, 0.1);
            color: #6366f1;
            border-radius: 6px;
            padding: 2px 8px;
            font-size: 12px;
            font-weight: 500;
        }
        .tab {
            padding: 10px 0;
            font-size: 14px;
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            color: #6366f1;
            border-bottom: 2px solid #6366f1;
            font-weight: 500;
        }
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .feature-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: rgba(99, 102, 241, 0.1);
            color: #6366f1;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
        }
        .bottom-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 100;
        }
    </style>
</head>
<body>
    <!-- 顶部图片 -->
    <div class="header">
        <div class="back-button">
            <i class="lucide lucide-chevron-left"></i>
        </div>
        <div class="share-button">
            <i class="lucide lucide-share-2"></i>
        </div>
        <img src="https://images.unsplash.com/photo-1626785774573-4b799315345d?q=80&w=800&auto=format&fit=crop" alt="AI视频生成" class="w-full h-56 object-cover rounded-t-lg">
    </div>

    <!-- 服务信息 -->
    <div class="p-4">
        <div class="flex justify-between items-start mb-3">
            <div class="flex items-center">
                <img src="https://images.unsplash.com/photo-1626785774573-4b799315345d?q=80&w=50&auto=format&fit=crop" class="w-10 h-10 rounded-lg object-cover mr-3" alt="AI视频生成">
                <h1 class="text-xl font-bold">AI視頻生成與定制服務</h1>
            </div>
            <div class="flex items-center text-sm">
                <i class="lucide lucide-star-filled text-yellow-500"></i>
                <span class="ml-1 text-gray-600">4.9 (198)</span>
            </div>
        </div>
        <div class="flex space-x-2 mb-4">
            <span class="tag">AI生成</span>
            <span class="tag">视频定制</span>
            <span class="tag">专业版</span>
        </div>
        <div class="flex justify-between items-center mb-6">
            <div>
                <span class="text-2xl font-bold text-indigo-600">¥2599</span>
                <span class="text-gray-500 line-through ml-2">¥3599</span>
            </div>
            <div class="flex items-center text-sm text-gray-600">
                <i class="lucide lucide-shopping-bag mr-1"></i>
                <span>198</span>
                <span class="mx-2">|</span>
                <i class="lucide lucide-thumbs-up mr-1"></i>
                <span>99%</span>
            </div>
        </div>

        <!-- 服务标签 -->
        <div class="flex justify-between border-b mb-4">
            <div class="tab active text-center flex-1">服务详情</div>
            <div class="tab text-gray-600 text-center flex-1">客户评价</div>
            <div class="tab text-gray-600 text-center flex-1">常见问题</div>
        </div>

        <!-- 服务详情 -->
        <div>
            <div class="glass-card p-4 mb-4">
                <h3 class="font-semibold mb-3">服務介紹</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    AI视频生成与定制服务是基于最新生成式AI技术的专业视频创作平台，通过先进的深度学习模型，实现从文本到视频的智能生成和高度定制化。相比传统视频制作，我们的服务能够快速生成高质量视频内容，并支持个性化定制，特别适合企业宣传、产品展示和教育培训等场景。
                </p>
            </div>

            <div class="glass-card p-4 mb-4">
                <h3 class="font-semibold mb-3">服務內容</h3>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="lucide lucide-network"></i>
                    </div>
                    <div>
                        <h4 class="font-medium">智能视频生成</h4>
                        <p class="text-gray-600 text-sm">从文本描述自动生成高质量视频内容</p>
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="lucide lucide-git-branch"></i>
                    </div>
                    <div>
                        <h4 class="font-medium">风格定制</h4>
                        <p class="text-gray-600 text-sm">支持多种视频风格和主题的个性化定制</p>
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="lucide lucide-message-square"></i>
                    </div>
                    <div>
                        <h4 class="font-medium">多语言支持</h4>
                        <p class="text-gray-600 text-sm">支持多种语言的视频生成和字幕</p>
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="lucide lucide-bar-chart"></i>
                    </div>
                    <div>
                        <h4 class="font-medium">专业配音</h4>
                        <p class="text-gray-600 text-sm">提供多种语音风格的专业配音服务</p>
                    </div>
                </div>
            </div>

            <div class="glass-card p-4 mb-4">
                <h3 class="font-semibold mb-3">適用場景</h3>
                <div class="grid grid-cols-2 gap-3">
                    <div class="bg-indigo-50 p-3 rounded-lg">
                        <div class="flex items-center mb-2">
                            <i class="lucide lucide-stethoscope text-indigo-600 mr-2"></i>
                            <span class="font-medium">医疗健康</span>
                        </div>
                        <p class="text-gray-600 text-xs">疾病诊断与药物关系分析</p>
                    </div>
                    <div class="bg-indigo-50 p-3 rounded-lg">
                        <div class="flex items-center mb-2">
                            <i class="lucide lucide-briefcase text-indigo-600 mr-2"></i>
                            <span class="font-medium">金融分析</span>
                        </div>
                        <p class="text-gray-600 text-xs">投资关系与风险评估</p>
                    </div>
                    <div class="bg-indigo-50 p-3 rounded-lg">
                        <div class="flex items-center mb-2">
                            <i class="lucide lucide-book-open text-indigo-600 mr-2"></i>
                            <span class="font-medium">学术研究</span>
                        </div>
                        <p class="text-gray-600 text-xs">文献关联与研究趋势分析</p>
                    </div>
                    <div class="bg-indigo-50 p-3 rounded-lg">
                        <div class="flex items-center mb-2">
                            <i class="lucide lucide-users text-indigo-600 mr-2"></i>
                            <span class="font-medium">客户关系</span>
                        </div>
                        <p class="text-gray-600 text-xs">客户网络与购买行为分析</p>
                    </div>
                </div>
            </div>

            <div class="glass-card p-4 mb-4">
                <h3 class="font-semibold mb-3">服務流程</h3>
                <div class="relative pl-8 pb-1">
                    <div class="absolute left-3 top-0 bottom-0 w-0.5 bg-indigo-100"></div>
                    <div class="absolute left-0 top-0 w-6 h-6 rounded-full bg-indigo-600 flex items-center justify-center text-white text-xs">1</div>
                    <div class="mb-4">
                        <h4 class="font-medium">数据评估</h4>
                        <p class="text-gray-600 text-sm">评估企业数据结构与知识图谱需求</p>
                    </div>
                </div>
                <div class="relative pl-8 pb-1">
                    <div class="absolute left-3 top-0 bottom-0 w-0.5 bg-indigo-100"></div>
                    <div class="absolute left-0 top-0 w-6 h-6 rounded-full bg-indigo-600 flex items-center justify-center text-white text-xs">2</div>
                    <div class="mb-4">
                        <h4 class="font-medium">图谱构建</h4>
                        <p class="text-gray-600 text-sm">实体抽取与关系建模</p>
                    </div>
                </div>
                <div class="relative pl-8 pb-1">
                    <div class="absolute left-3 top-0 bottom-0 w-0.5 bg-indigo-100"></div>
                    <div class="absolute left-0 top-0 w-6 h-6 rounded-full bg-indigo-600 flex items-center justify-center text-white text-xs">3</div>
                    <div class="mb-4">
                        <h4 class="font-medium">系统开发</h4>
                        <p class="text-gray-600 text-sm">GraphRAG引擎开发与模型训练</p>
                    </div>
                </div>
                <div class="relative pl-8">
                    <div class="absolute left-0 top-0 w-6 h-6 rounded-full bg-indigo-600 flex items-center justify-center text-white text-xs">4</div>
                    <div class="mb-1">
                        <h4 class="font-medium">部署与迭代</h4>
                        <p class="text-gray-600 text-sm">系统上线与图谱持续更新</p>
                    </div>
                </div>
            </div>

            <!-- 成功案例 -->
            <div class="glass-card p-4 mb-20">
                <h3 class="font-semibold mb-3">成功案例</h3>
                <div class="mb-4 pb-4 border-b border-gray-100">
                    <div class="flex items-center mb-2">
                        <img src="https://images.unsplash.com/photo-1551836022-d5d88e9218df?q=80&w=50&auto=format&fit=crop" class="w-8 h-8 rounded-full mr-2" alt="客户头像">
                        <h4 class="font-medium">某教育科技公司</h4>
                    </div>
                    <p class="text-gray-600 text-sm mb-2">利用我们的AI视频生成服务，课程制作效率提升80%，视频内容质量显著提高，学员满意度达到98%。</p>
                    <div class="flex items-center text-yellow-500 text-xs">
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-filled"></i>
                    </div>
                </div>
                <div>
                    <div class="flex items-center mb-2">
                        <img src="https://images.unsplash.com/photo-1444653614773-995cb1ef9efa?q=80&w=50&auto=format&fit=crop" class="w-8 h-8 rounded-full mr-2" alt="客户头像">
                        <h4 class="font-medium">某金融分析机构</h4>
                    </div>
                    <p class="text-gray-600 text-sm mb-2">通过GraphRAG构建投资关系网络分析系统，风险预警准确率提升60%，分析师工作效率提高50%，为投资决策提供了关键支持。</p>
                    <div class="flex items-center text-yellow-500 text-xs">
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-half"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部购买栏 -->
    <div class="bottom-bar">
        <div class="flex items-center">
            <div class="mr-4 text-center">
                <i class="lucide lucide-message-circle text-gray-600"></i>
                <div class="text-xs text-gray-600">咨询</div>
            </div>
            <div class="text-center">
                <i class="lucide lucide-heart text-gray-600"></i>
                <div class="text-xs text-gray-600">收藏</div>
            </div>
        </div>
        <div class="flex">
            <button class="px-4 py-2 bg-indigo-100 text-indigo-600 rounded-l-full font-medium">免费咨询</button>
            <button class="px-4 py-2 bg-indigo-600 text-white rounded-r-full font-medium">立即購買</button>
        </div>
    </div>
</body>
</html>