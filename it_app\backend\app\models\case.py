"""
AI 案例相關數據模型
"""

from decimal import Decimal
from typing import Optional, List
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, Numeric, String, Text, JSON, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import Base
from .base import TimestampMixin


class AICase(Base, TimestampMixin):
    """AI 案例模型"""
    
    __tablename__ = "ai_cases"
    
    # 主鍵
    id: Mapped[int] = mapped_column(
        Integer, 
        primary_key=True, 
        autoincrement=True,
        comment="案例ID"
    )
    
    # 基本信息
    title: Mapped[str] = mapped_column(
        String(200), 
        nullable=False,
        comment="案例標題"
    )
    
    subtitle: Mapped[Optional[str]] = mapped_column(
        String(300), 
        nullable=True,
        comment="案例副標題"
    )
    
    description: Mapped[str] = mapped_column(
        Text, 
        nullable=False,
        comment="案例簡介"
    )
    
    detailed_description: Mapped[Optional[str]] = mapped_column(
        Text, 
        nullable=True,
        comment="詳細描述"
    )
    
    # 技術信息
    technologies: Mapped[Optional[str]] = mapped_column(
        JSON, 
        nullable=True,
        comment="使用的AI技術列表"
    )
    
    category: Mapped[str] = mapped_column(
        String(50), 
        nullable=False,
        comment="案例分類"
    )
    
    industry: Mapped[Optional[str]] = mapped_column(
        String(100), 
        nullable=True,
        comment="所屬行業"
    )
    
    # 項目信息
    client_name: Mapped[Optional[str]] = mapped_column(
        String(200), 
        nullable=True,
        comment="客戶名稱"
    )
    
    project_duration: Mapped[Optional[str]] = mapped_column(
        String(100), 
        nullable=True,
        comment="項目週期"
    )
    
    project_budget: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(12, 2), 
        nullable=True,
        comment="項目預算"
    )
    
    # 成果信息
    achievements: Mapped[Optional[str]] = mapped_column(
        JSON, 
        nullable=True,
        comment="項目成果列表"
    )
    
    benefits: Mapped[Optional[str]] = mapped_column(
        Text, 
        nullable=True,
        comment="項目效益"
    )
    
    roi_percentage: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(5, 2), 
        nullable=True,
        comment="投資回報率"
    )
    
    # 媒體信息
    cover_image: Mapped[Optional[str]] = mapped_column(
        String(500), 
        nullable=True,
        comment="封面圖片URL"
    )
    
    gallery_images: Mapped[Optional[str]] = mapped_column(
        JSON, 
        nullable=True,
        comment="案例圖片庫"
    )
    
    demo_video: Mapped[Optional[str]] = mapped_column(
        String(500), 
        nullable=True,
        comment="演示視頻URL"
    )
    
    # 評價信息
    client_rating: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(3, 2), 
        nullable=True,
        comment="客戶評分"
    )
    
    # 狀態信息
    is_featured: Mapped[bool] = mapped_column(
        Boolean, 
        default=False, 
        nullable=False,
        comment="是否為精選案例"
    )
    
    is_published: Mapped[bool] = mapped_column(
        Boolean, 
        default=True, 
        nullable=False,
        comment="是否已發布"
    )
    
    view_count: Mapped[int] = mapped_column(
        Integer, 
        default=0, 
        nullable=False,
        comment="瀏覽次數"
    )
    
    # 關聯關係
    testimonials: Mapped[List["CaseTestimonial"]] = relationship(
        "CaseTestimonial", 
        back_populates="case",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<AICase(id={self.id}, title='{self.title}', category='{self.category}')>"


class CaseTestimonial(Base, TimestampMixin):
    """案例客戶評價模型"""
    
    __tablename__ = "case_testimonials"
    
    # 主鍵
    id: Mapped[int] = mapped_column(
        Integer, 
        primary_key=True, 
        autoincrement=True,
        comment="評價ID"
    )
    
    # 外鍵
    case_id: Mapped[int] = mapped_column(
        Integer, 
        ForeignKey("ai_cases.id", ondelete="CASCADE"), 
        nullable=False,
        comment="案例ID"
    )
    
    # 評價信息
    client_name: Mapped[str] = mapped_column(
        String(100), 
        nullable=False,
        comment="客戶姓名"
    )
    
    client_title: Mapped[Optional[str]] = mapped_column(
        String(100), 
        nullable=True,
        comment="客戶職位"
    )
    
    client_company: Mapped[Optional[str]] = mapped_column(
        String(200), 
        nullable=True,
        comment="客戶公司"
    )
    
    client_avatar: Mapped[Optional[str]] = mapped_column(
        String(500), 
        nullable=True,
        comment="客戶頭像URL"
    )
    
    content: Mapped[str] = mapped_column(
        Text, 
        nullable=False,
        comment="評價內容"
    )
    
    rating: Mapped[Decimal] = mapped_column(
        Numeric(3, 2), 
        nullable=False,
        comment="評分"
    )
    
    is_featured: Mapped[bool] = mapped_column(
        Boolean, 
        default=False, 
        nullable=False,
        comment="是否為精選評價"
    )
    
    # 關聯關係
    case: Mapped["AICase"] = relationship("AICase", back_populates="testimonials")
    
    def __repr__(self) -> str:
        return f"<CaseTestimonial(id={self.id}, case_id={self.case_id}, client='{self.client_name}')>"
