import React, { InputHTMLAttributes, ReactNode, forwardRef } from 'react';
import clsx from 'clsx';

interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  variant?: 'default' | 'filled';
}

const Input = forwardRef<HTMLInputElement, InputProps>(({
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  variant = 'default',
  className,
  id,
  ...props
}, ref) => {
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;

  const baseClasses = [
    'w-full px-4 py-3 rounded-lg border transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-offset-1',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    'placeholder:text-gray-400'
  ];

  const variantClasses = {
    default: [
      'bg-white border-gray-300',
      'focus:border-primary-500 focus:ring-primary-200',
      error ? 'border-red-300 focus:border-red-500 focus:ring-red-200' : ''
    ],
    filled: [
      'bg-gray-50 border-transparent',
      'focus:bg-white focus:border-primary-500 focus:ring-primary-200',
      error ? 'bg-red-50 border-red-300 focus:border-red-500 focus:ring-red-200' : ''
    ]
  };

  const iconClasses = 'w-5 h-5 text-gray-400';

  return (
    <div className="w-full">
      {/* 標籤 */}
      {label && (
        <label 
          htmlFor={inputId} 
          className="block text-sm font-medium text-gray-700 mb-2"
        >
          {label}
        </label>
      )}

      {/* 輸入框容器 */}
      <div className="relative">
        {/* 左側圖標 */}
        {leftIcon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span className={iconClasses}>
              {leftIcon}
            </span>
          </div>
        )}

        {/* 輸入框 */}
        <input
          ref={ref}
          id={inputId}
          className={clsx(
            baseClasses,
            variantClasses[variant],
            leftIcon && 'pl-10',
            rightIcon && 'pr-10',
            className
          )}
          {...props}
        />

        {/* 右側圖標 */}
        {rightIcon && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <span className={iconClasses}>
              {rightIcon}
            </span>
          </div>
        )}
      </div>

      {/* 錯誤信息或幫助文本 */}
      {(error || helperText) && (
        <div className="mt-2">
          {error ? (
            <p className="text-sm text-red-600 flex items-center">
              <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path 
                  fillRule="evenodd" 
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" 
                  clipRule="evenodd" 
                />
              </svg>
              {error}
            </p>
          ) : (
            <p className="text-sm text-gray-500">{helperText}</p>
          )}
        </div>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input; 