<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI速應 - 英伦奢华智能服务体验</title>
    <link href="https://fonts.googleapis.com/css2?family=Bodoni+Moda:wght@400;600;700&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link href="https://unpkg.com/lucide-static/font/lucide.css" rel="stylesheet">
    <style>
        :root {
            --burberry-beige: #D4B896;
            --burberry-black: #000000;
            --burberry-white: #FFFFFF;
            --burberry-red: #C8102E;
            --burberry-dark-grey: #333333;
            --burberry-light-grey: #F5F5F5;
            --burberry-check: #C8102E;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--burberry-white);
            color: var(--burberry-black);
            line-height: 1.6;
            overflow-x: hidden;
        }

        .bodoni {
            font-family: 'Bodoni Moda', serif;
        }

        /* 导航栏 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 20px 40px;
            background: rgba(255, 255, 255, 0);
            backdrop-filter: blur(10px);
            transition: all 0.6s ease;
        }

        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.95);
            border-bottom: 1px solid rgba(212, 184, 150, 0.2);
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo {
            font-size: 24px;
            font-weight: 600;
            color: var(--burberry-black);
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            gap: 40px;
            list-style: none;
        }

        .nav-links a {
            color: var(--burberry-black);
            text-decoration: none;
            font-weight: 400;
            position: relative;
            transition: all 0.4s ease;
        }

        .nav-links a:hover {
            color: var(--burberry-red);
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 1px;
            background: var(--burberry-red);
            transition: width 0.4s ease;
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        /* 英雄区 */
        .hero-section {
            height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--burberry-light-grey) 0%, var(--burberry-beige) 100%);
            overflow: hidden;
        }

        .hero-pattern {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(212, 184, 150, 0.1) 10px, rgba(212, 184, 150, 0.1) 20px),
                repeating-linear-gradient(-45deg, transparent, transparent 10px, rgba(0, 0, 0, 0.05) 10px, rgba(0, 0, 0, 0.05) 20px);
            opacity: 0.3;
        }

        /* Burberry格紋背景 */
        .burberry-check {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(to right, transparent 95%, var(--burberry-check) 95%, var(--burberry-check) 100%),
                linear-gradient(to bottom, transparent 95%, var(--burberry-check) 95%, var(--burberry-check) 100%);
            background-size: 50px 50px;
            opacity: 0.05;
            z-index: 1;
        }

        .hero-content {
            text-align: center;
            z-index: 2;
            max-width: 800px;
            padding: 0 40px;
        }

        .hero-title {
            font-size: clamp(3rem, 8vw, 6rem);
            font-weight: 700;
            color: var(--burberry-black);
            margin-bottom: 30px;
            letter-spacing: -2px;
        }

        .hero-subtitle {
            font-size: 1.5rem;
            color: var(--burberry-dark-grey);
            margin-bottom: 50px;
            font-weight: 300;
        }

        .cta-button {
            display: inline-block;
            padding: 18px 40px;
            background: transparent;
            border: 2px solid var(--burberry-black);
            color: var(--burberry-black);
            text-decoration: none;
            font-weight: 500;
            letter-spacing: 1px;
            transition: all 0.6s ease;
            position: relative;
            overflow: hidden;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--burberry-black);
            transition: left 0.6s ease;
            z-index: -1;
        }

        .cta-button:hover::before {
            left: 0;
        }

        .cta-button:hover {
            color: var(--burberry-white);
        }

        /* 内容区块 */
        .content-section {
            padding: 120px 0;
            position: relative;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 40px;
        }

        .section-title {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 600;
            color: var(--burberry-black);
            margin-bottom: 60px;
            text-align: center;
        }

        /* 品牌故事区 */
        .story-section {
            background: var(--burberry-light-grey);
            position: relative;
            overflow: hidden;
        }

        /* 添加Burberry格紋元素 */
        .story-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(to right, transparent 95%, var(--burberry-beige) 95%, var(--burberry-beige) 100%),
                linear-gradient(to bottom, transparent 95%, var(--burberry-beige) 95%, var(--burberry-beige) 100%);
            background-size: 50px 50px;
            opacity: 0.1;
            z-index: 0;
        }

        .story-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .story-content h3 {
            font-size: 2rem;
            margin-bottom: 30px;
            color: var(--burberry-black);
        }

        .story-content p {
            font-size: 1.1rem;
            color: var(--burberry-dark-grey);
            margin-bottom: 20px;
            line-height: 1.8;
        }

        .story-image {
            position: relative;
            overflow: hidden;
        }

        .story-image img {
            width: 100%;
            height: 500px;
            object-fit: cover;
            filter: grayscale(20%);
            transition: transform 0.6s ease;
        }

        .story-image:hover img {
            transform: scale(1.05);
        }

        /* 服务展示区 */
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 60px;
            margin-top: 80px;
        }

        .service-card {
            background: var(--burberry-white);
            padding: 50px 40px;
            border: 1px solid rgba(212, 184, 150, 0.3);
            transition: all 0.6s ease;
            position: relative;
            overflow: hidden;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--burberry-beige);
            transform: scaleX(0);
            transition: transform 0.6s ease;
        }

        .service-card:hover::before {
            transform: scaleX(1);
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .service-icon {
            width: 60px;
            height: 60px;
            background: var(--burberry-beige);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
            color: var(--burberry-black);
        }

        .service-card h4 {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: var(--burberry-black);
        }

        .service-card p {
            color: var(--burberry-dark-grey);
            line-height: 1.7;
        }

        /* 工藝展示区 */
        .craft-section {
            background: var(--burberry-black);
            color: var(--burberry-white);
            position: relative;
            overflow: hidden;
        }

        /* 添加Burberry格紋元素 */
        .craft-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(to right, transparent 95%, var(--burberry-red) 95%, var(--burberry-red) 100%),
                linear-gradient(to bottom, transparent 95%, var(--burberry-red) 95%, var(--burberry-red) 100%);
            background-size: 50px 50px;
            opacity: 0.05;
            z-index: 0;
        }

        .craft-section .section-title {
            color: var(--burberry-white);
            position: relative;
            z-index: 1;
        }

        .craft-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 40px;
            margin-top: 80px;
            position: relative;
            z-index: 1;
        }

        .craft-item {
            text-align: center;
            padding: 40px 20px;
        }

        .craft-item img {
            width: 100%;
            height: 250px;
            object-fit: cover;
            filter: grayscale(100%);
            margin-bottom: 30px;
            transition: filter 0.6s ease;
        }

        .craft-item:hover img {
            filter: grayscale(0%);
        }

        .craft-item h4 {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: var(--burberry-beige);
        }

        .craft-item p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.95rem;
        }

        /* 價格區 */
        .pricing-section {
            background: var(--burberry-beige);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        /* 添加Burberry格紋元素 */
        .pricing-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(to right, transparent 95%, rgba(255, 255, 255, 0.3) 95%, rgba(255, 255, 255, 0.3) 100%),
                linear-gradient(to bottom, transparent 95%, rgba(255, 255, 255, 0.3) 95%, rgba(255, 255, 255, 0.3) 100%);
            background-size: 50px 50px;
            opacity: 0.2;
            z-index: 0;
        }

        .pricing-card {
            background: var(--burberry-white);
            padding: 60px 40px;
            max-width: 500px;
            margin: 0 auto;
            border: 1px solid rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
        }

        .pricing-card::before {
            content: 'PREMIUM';
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--burberry-red);
            color: var(--burberry-white);
            padding: 8px 20px;
            font-size: 0.8rem;
            letter-spacing: 2px;
            font-weight: 600;
        }

        .price {
            font-size: 3rem;
            font-weight: 700;
            color: var(--burberry-black);
            margin-bottom: 10px;
        }

        .price-subtitle {
            color: var(--burberry-dark-grey);
            margin-bottom: 40px;
        }

        .features-list {
            list-style: none;
            margin-bottom: 40px;
        }

        .features-list li {
            padding: 10px 0;
            border-bottom: 1px solid rgba(212, 184, 150, 0.2);
            color: var(--burberry-dark-grey);
        }

        .features-list li:last-child {
            border-bottom: none;
        }

        /* 頁腳 */
        .footer {
            background: var(--burberry-black);
            color: var(--burberry-white);
            padding: 80px 0 40px;
            position: relative;
            overflow: hidden;
        }

        /* 添加Burberry格紋元素 */
        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(to right, transparent 95%, var(--burberry-beige) 95%, var(--burberry-beige) 100%),
                linear-gradient(to bottom, transparent 95%, var(--burberry-beige) 95%, var(--burberry-beige) 100%);
            background-size: 50px 50px;
            opacity: 0.05;
            z-index: 0;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 60px;
            margin-bottom: 60px;
            position: relative;
            z-index: 1;
        }

        .footer-column h4 {
            color: var(--burberry-beige);
            margin-bottom: 20px;
            font-size: 1.1rem;
        }

        .footer-column ul {
            list-style: none;
        }

        .footer-column ul li {
            margin-bottom: 10px;
        }

        .footer-column ul li a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: color 0.4s ease;
        }

        .footer-column ul li a:hover {
            color: var(--burberry-beige);
        }

        .footer-bottom {
            border-top: 1px solid rgba(212, 184, 150, 0.2);
            padding-top: 40px;
            text-align: center;
            color: rgba(255, 255, 255, 0.5);
            position: relative;
            z-index: 1;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            .navbar {
                padding: 15px 20px;
            }

            .nav-links {
                display: none;
            }

            .hero-content {
                padding: 0 20px;
            }

            .container {
                padding: 0 20px;
            }

            .story-grid {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .services-grid {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .craft-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .footer-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 40px;
            }
        }

        /* 滾動動畫 */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* 加載動畫 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--burberry-white);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.6s ease;
        }

        .loading-logo {
            font-size: 2rem;
            font-weight: 600;
            color: var(--burberry-black);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <!-- 加载动画 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-logo bodoni">AI速應</div>
    </div>

    <!-- 导航栏 -->
    <nav class="navbar" id="navbar">
        <div class="nav-content">
            <a href="#" class="logo bodoni">AI速應</a>
            <ul class="nav-links">
                <li><a href="#home">首页</a></li>
                <li><a href="#services">服务</a></li>
                <li><a href="#craft">工艺</a></li>
                <li><a href="#pricing">定价</a></li>
                <li><a href="#contact">联系</a></li>
            </ul>
        </div>
    </nav>

    <!-- 英雄区 -->
    <section class="hero-section" id="home">
        <div class="hero-pattern"></div>
        <div class="burberry-check"></div>
        <div class="hero-content">
            <h1 class="hero-title bodoni fade-in">Agentic RAG</h1>
            <p class="hero-subtitle fade-in">英伦奢华智能服务体验</p>
            <a href="#services" class="cta-button fade-in">探索服务</a>
        </div>
    </section>

    <!-- 品牌故事区 -->
    <section class="content-section story-section" id="story">
        <div class="container">
            <div class="story-grid">
                <div class="story-content fade-in">
                    <h3 class="bodoni">传承与创新的完美融合</h3>
                    <p>自1856年以来，我们始终坚持将传统英伦工艺与现代科技完美结合。Agentic RAG智能服务体系承载着这一传统，为每一位客户提供独一无二的奢华体验。</p>
                    <p>我们的智能代理系统不仅仅是技术的展现，更是对卓越品质的不懈追求。每一个细节都经过精心雕琢，确保为您带来超越期待的服务体验。</p>
                </div>
                <div class="story-image fade-in">
                    <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?q=80&w=800&auto=format&fit=crop" alt="英伦工艺">
                </div>
            </div>
        </div>
    </section>

    <!-- 服务展示区 -->
    <section class="content-section" id="services">
        <div class="container">
            <h2 class="section-title bodoni fade-in">奢华服务体验</h2>
            <div class="services-grid">
                <div class="service-card fade-in">
                    <div class="service-icon">
                        <i class="lucide lucide-brain"></i>
                    </div>
                    <h4 class="bodoni">智能代理引擎</h4>
                    <p>采用最先进的AI技术，为您提供自主规划与决策的智能代理系统，如同拥有一位专属的英伦管家。</p>
                </div>
                <div class="service-card fade-in">
                    <div class="service-icon">
                        <i class="lucide lucide-tool"></i>
                    </div>
                    <h4 class="bodoni">工具集成能力</h4>
                    <p>无缝集成各类外部工具与API，为您完成复杂任务，展现出色的执行能力与效率。</p>
                </div>
                <div class="service-card fade-in">
                    <div class="service-icon">
                        <i class="lucide lucide-database"></i>
                    </div>
                    <h4 class="bodoni">增強知識庫</h4>
                    <p>構建專屬的智能知識庫，結合結構化與非結構化數據，為您提供精準的信息檢索服務。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 工藝展示區 -->
    <section class="content-section craft-section" id="craft">
        <div class="container">
            <h2 class="section-title bodoni fade-in">精密工藝</h2>
            <div class="craft-grid">
                <div class="craft-item fade-in">
                    <img src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?q=80&w=400&auto=format&fit=crop" alt="精密工藝">
                    <h4>精密算法</h4>
                    <p>每一行代碼都經過精心雕琢，確保系統的穩定性與高效性。</p>
                </div>
                <div class="craft-item fade-in">
                    <img src="https://images.unsplash.com/photo-1518709268805-4e9042af2176?q=80&w=400&auto=format&fit=crop" alt="品質檢測">
                    <h4>品質保證</h4>
                    <p>嚴格的質量控制流程，確保每一個功能都達到奢華品質標準。</p>
                </div>
                <div class="craft-item fade-in">
                    <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?q=80&w=400&auto=format&fit=crop" alt="個性定制">
                    <h4>個性定制</h4>
                    <p>根據您的獨特需求，量身定制專屬的智能服務解決方案。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 價格區 -->
    <section class="content-section pricing-section" id="pricing">
        <div class="container">
            <h2 class="section-title bodoni fade-in">奢華定價</h2>
            <div class="pricing-card fade-in">
                <div class="price bodoni">¥3,299</div>
                <p class="price-subtitle">英倫奢華版</p>
                <ul class="features-list">
                    <li>智能代理引擎</li>
                    <li>工具集成能力</li>
                    <li>增強知識庫</li>
                    <li>流程自動化</li>
                    <li>24/7專屬支持</li>
                    <li>個性化定制</li>
                </ul>
                <a href="#" class="cta-button">立即體驗</a>
            </div>
        </div>
    </section>

    <!-- 頁腳 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-column">
                    <h4>服務</h4>
                    <ul>
                        <li><a href="#">智能代理</a></li>
                        <li><a href="#">知識庫構建</a></li>
                        <li><a href="#">流程自動化</a></li>
                        <li><a href="#">定制開發</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h4>支持</h4>
                    <ul>
                        <li><a href="#">客戶服務</a></li>
                        <li><a href="#">技術支持</a></li>
                        <li><a href="#">使用指南</a></li>
                        <li><a href="#">常見問題</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h4>公司</h4>
                    <ul>
                        <li><a href="#">關於我們</a></li>
                        <li><a href="#">新聞動態</a></li>
                        <li><a href="#">職業機會</a></li>
                        <li><a href="#">投資者關係</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h4>聯繫</h4>
                    <ul>
                        <li><a href="#">聯繫我們</a></li>
                        <li><a href="#">合作夥伴</a></li>
                        <li><a href="#">媒體諮詢</a></li>
                        <li><a href="#">法律條款</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 AI速應. 保留所有權利. | 隱私政策 | 使用條款</p>
            </div>
        </div>
    </footer>

    <script>
        // 頁面加載動畫
        window.addEventListener('load', function() {
            setTimeout(() => {
                document.getElementById('loadingOverlay').style.opacity = '0';
                setTimeout(() => {
                    document.getElementById('loadingOverlay').style.display = 'none';
                }, 600);
            }, 1500);
        });

        // 導航欄滾動效果
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // 滾動動畫
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // 平滑滾動
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 視差滾動效果
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.hero-pattern');
            if (parallax) {
                parallax.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        });
    </script>
</body>
</html>