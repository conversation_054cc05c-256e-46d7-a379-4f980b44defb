import React from 'react';
import { AlertTriangle, <PERSON>, <PERSON><PERSON><PERSON>, Zap } from 'lucide-react';
import { usePerformanceWarning } from '@/hooks/usePerformanceMonitor';

interface PerformanceWarningProps {
  onOptimize?: () => void;
}

const PerformanceWarning: React.FC<PerformanceWarningProps> = ({ onOptimize }) => {
  const { showWarning, warningMessage, isLowPerformance, dismissWarning } = usePerformanceWarning();

  if (!showWarning) return null;

  return (
    <div className="fixed top-4 left-4 right-4 z-50 max-w-md mx-auto">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-lg">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <AlertTriangle className="w-5 h-5 text-yellow-600" />
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-medium text-yellow-800 mb-1">
              性能提醒
            </h3>
            <p className="text-sm text-yellow-700 mb-3">
              {warningMessage}
            </p>
            
            <div className="flex items-center space-x-2">
              {onOptimize && (
                <button
                  onClick={() => {
                    onOptimize();
                    dismissWarning();
                  }}
                  className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-yellow-800 bg-yellow-100 border border-yellow-300 rounded-md hover:bg-yellow-200 transition-colors"
                >
                  <Zap className="w-3 h-3 mr-1" />
                  自動優化
                </button>
              )}
              
              <button
                onClick={dismissWarning}
                className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-yellow-800 bg-transparent border border-yellow-300 rounded-md hover:bg-yellow-100 transition-colors"
              >
                知道了
              </button>
            </div>
          </div>
          
          <button
            onClick={dismissWarning}
            className="flex-shrink-0 p-1 rounded-md hover:bg-yellow-100 transition-colors"
          >
            <X className="w-4 h-4 text-yellow-600" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default PerformanceWarning;
