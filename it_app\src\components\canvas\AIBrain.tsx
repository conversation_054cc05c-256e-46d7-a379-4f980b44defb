'use client'

import { useRef, useMemo } from 'react'
import { useFrame } from '@react-three/fiber'
import { Sphere, MeshDistortMaterial } from '@react-three/drei'
import * as THREE from 'three'

interface AIBrainProps {
  animated?: boolean
  color?: string
  position?: [number, number, number]
}

export function AIBrain({ 
  animated = true, 
  color = '#3b82f6',
  position = [0, 0, 0]
}: AIBrainProps) {
  const meshRef = useRef<THREE.Mesh>(null)
  const materialRef = useRef<any>(null)

  // 創建神經網絡節點
  const nodes = useMemo(() => {
    const nodePositions = []
    for (let i = 0; i < 50; i++) {
      nodePositions.push([
        (Math.random() - 0.5) * 4,
        (Math.random() - 0.5) * 4,
        (Math.random() - 0.5) * 4,
      ])
    }
    return nodePositions
  }, [])

  useFrame((state) => {
    if (!animated) return
    
    if (meshRef.current) {
      meshRef.current.rotation.x += 0.005
      meshRef.current.rotation.y += 0.01
    }

    if (materialRef.current) {
      materialRef.current.distort = 0.3 + Math.sin(state.clock.elapsedTime) * 0.1
    }
  })

  return (
    <group position={position}>
      {/* 主要大腦球體 */}
      <Sphere ref={meshRef} args={[1.5, 64, 64]}>
        <MeshDistortMaterial
          ref={materialRef}
          color={color}
          transparent
          opacity={0.8}
          distort={0.3}
          speed={2}
          roughness={0.4}
          metalness={0.1}
        />
      </Sphere>

      {/* 神經網絡節點 */}
      {nodes.map((nodePos, index) => (
        <mesh key={index} position={nodePos as [number, number, number]}>
          <sphereGeometry args={[0.02, 8, 8]} />
          <meshBasicMaterial 
            color={color} 
            transparent 
            opacity={0.6}
          />
        </mesh>
      ))}

      {/* 環境光效 */}
      <pointLight 
        position={[2, 2, 2]} 
        intensity={0.5} 
        color={color}
      />
    </group>
  )
}
