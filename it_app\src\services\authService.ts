import { apiRequest } from './api';
import { 
  LoginRequest, 
  RegisterRequest, 
  AuthResponse, 
  User,
  UserProfile,
  SecuritySettings,
  NotificationSettings,
  CustomizationSettings
} from '@/types';

// 用戶認證服務
export const authService = {
  // 用戶登錄
  login: async (credentials: LoginRequest): Promise<AuthResponse> => {
    try {
      const response = await apiRequest.post<AuthResponse['data']>('/login', credentials);
      
      if (response.code === 200 && response.data) {
        // 保存token和用戶信息到localStorage
        localStorage.setItem('authToken', response.data.token);
        localStorage.setItem('userId', response.data.userId.toString());
      }
      
      return response as AuthResponse;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  },

  // 用戶註冊
  register: async (userData: RegisterRequest): Promise<AuthResponse> => {
    try {
      const response = await apiRequest.post<AuthResponse['data']>('/register', userData);
      
      if (response.code === 200 && response.data) {
        // 註冊成功後自動保存token
        localStorage.setItem('authToken', response.data.token);
        localStorage.setItem('userId', response.data.userId.toString());
      }
      
      return response as AuthResponse;
    } catch (error) {
      console.error('Register error:', error);
      throw error;
    }
  },

  // 第三方登錄
  thirdPartyLogin: async (platform: string, code: string): Promise<AuthResponse> => {
    try {
      const response = await apiRequest.post<AuthResponse['data']>('/login/third-party', {
        platform,
        code
      });
      
      if (response.code === 200 && response.data) {
        localStorage.setItem('authToken', response.data.token);
        localStorage.setItem('userId', response.data.userId.toString());
      }
      
      return response as AuthResponse;
    } catch (error) {
      console.error('Third party login error:', error);
      throw error;
    }
  },

  // 重置密碼
  resetPassword: async (contact: string, verificationCode: string, newPassword: string) => {
    try {
      return await apiRequest.post('/reset-password', {
        contact,
        verificationCode,
        newPassword
      });
    } catch (error) {
      console.error('Reset password error:', error);
      throw error;
    }
  },

  // 獲取用戶資料
  getUserProfile: async (userId: number): Promise<User> => {
    try {
      const response = await apiRequest.get<User>(`/users/profile/${userId}`);
      return response.data!;
    } catch (error) {
      console.error('Get user profile error:', error);
      throw error;
    }
  },

  // 更新用戶資料
  updateUserProfile: async (userId: number, profile: Partial<UserProfile>) => {
    try {
      return await apiRequest.put('/users/profile', {
        userId,
        ...profile
      });
    } catch (error) {
      console.error('Update user profile error:', error);
      throw error;
    }
  },

  // 更新安全設置
  updateSecuritySettings: async (userId: number, settings: Partial<SecuritySettings>) => {
    try {
      return await apiRequest.put('/users/security', {
        userId,
        ...settings
      });
    } catch (error) {
      console.error('Update security settings error:', error);
      throw error;
    }
  },

  // 更新通知偏好
  updateNotificationSettings: async (userId: number, settings: NotificationSettings) => {
    try {
      return await apiRequest.put('/users/notification', {
        userId,
        ...settings
      });
    } catch (error) {
      console.error('Update notification settings error:', error);
      throw error;
    }
  },

  // 更新個性化設置
  updateCustomizationSettings: async (userId: number, settings: CustomizationSettings) => {
    try {
      return await apiRequest.put('/users/customize', {
        userId,
        ...settings
      });
    } catch (error) {
      console.error('Update customization settings error:', error);
      throw error;
    }
  },

  // 獲取交易記錄
  getTransactionHistory: async (userId: number) => {
    try {
      return await apiRequest.get(`/users/transactions?userId=${userId}`);
    } catch (error) {
      console.error('Get transaction history error:', error);
      throw error;
    }
  },

  // 登出
  logout: () => {
    localStorage.removeItem('authToken');
    localStorage.removeItem('userId');
    localStorage.removeItem('user');
  },

  // 檢查是否已登錄
  isAuthenticated: (): boolean => {
    return !!localStorage.getItem('authToken');
  },

  // 獲取當前用戶ID
  getCurrentUserId: (): number | null => {
    const userId = localStorage.getItem('userId');
    return userId ? parseInt(userId, 10) : null;
  }
}; 