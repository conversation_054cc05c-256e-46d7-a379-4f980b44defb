"""
用戶管理 API 路由
"""

from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.dependencies import get_current_active_user, get_user_service
from app.schemas.common import ResponseModel
from app.schemas.user import UserUpdate, PasswordUpdate, UserProfile
from app.services.user_service import UserService
from app.models.user import User

router = APIRouter()


@router.get("/me", response_model=ResponseModel)
async def get_user_profile(
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    獲取用戶資料
    """
    # 構建用戶資料（包含統計信息）
    profile_data = UserProfile(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        nickname=current_user.nickname,
        avatar_url=current_user.avatar_url,
        login_provider=current_user.login_provider,
        # 模擬統計數據（實際應從數據庫查詢）
        completed_projects=12,
        customer_rating=4.8,
        growth_index=85
    )
    
    return ResponseModel(
        success=True,
        message="獲取用戶資料成功",
        data=profile_data
    )


@router.put("/me", response_model=ResponseModel)
async def update_user_profile(
    user_data: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    user_service: UserService = Depends(get_user_service)
) -> Any:
    """
    更新用戶資料
    """
    try:
        updated_user = await user_service.update_user(current_user, user_data)
        
        return ResponseModel(
            success=True,
            message="用戶資料更新成功",
            data={
                "id": updated_user.id,
                "nickname": updated_user.nickname,
                "avatar_url": updated_user.avatar_url
            }
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用戶資料失敗"
        )


@router.put("/me/password", response_model=ResponseModel)
async def update_password(
    password_data: PasswordUpdate,
    current_user: User = Depends(get_current_active_user),
    user_service: UserService = Depends(get_user_service)
) -> Any:
    """
    修改密碼
    """
    try:
        await user_service.update_password(current_user, password_data)
        
        return ResponseModel(
            success=True,
            message="密碼修改成功"
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="修改密碼失敗"
        )


@router.post("/me/avatar", response_model=ResponseModel)
async def upload_avatar(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user),
    user_service: UserService = Depends(get_user_service)
) -> Any:
    """
    上傳頭像
    """
    # 驗證文件類型
    if not file.content_type or not file.content_type.startswith("image/"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只能上傳圖片文件"
        )
    
    # 驗證文件大小（限制 5MB）
    if file.size and file.size > 5 * 1024 * 1024:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="文件大小不能超過 5MB"
        )
    
    try:
        # TODO: 實現文件上傳到雲存儲
        # 這裡暫時返回一個模擬的 URL
        avatar_url = f"https://example.com/avatars/{current_user.id}_{file.filename}"
        
        updated_user = await user_service.update_avatar(current_user, avatar_url)
        
        return ResponseModel(
            success=True,
            message="頭像上傳成功",
            data={"avatar_url": updated_user.avatar_url}
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="頭像上傳失敗"
        )


@router.delete("/me", response_model=ResponseModel)
async def deactivate_account(
    current_user: User = Depends(get_current_active_user),
    user_service: UserService = Depends(get_user_service)
) -> Any:
    """
    停用帳號
    """
    try:
        await user_service.deactivate_user(current_user)
        
        return ResponseModel(
            success=True,
            message="帳號已停用"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="停用帳號失敗"
        )
