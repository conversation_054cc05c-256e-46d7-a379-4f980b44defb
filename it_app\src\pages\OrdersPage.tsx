import React, { useState, Suspense, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Clock, CheckCircle, AlertCircle, XCircle, Eye, Lock, ShoppingBag } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import MobileLayout from '@/components/layout/MobileLayout';
import { View, Common } from '@/components/canvas/View';
import { Progress3D } from '@/components/canvas/Progress3D';
import { ParticleSystem } from '@/components/canvas/ParticleSystem';

// 訂單狀態類型
type OrderStatus = 'pending' | 'in_progress' | 'completed' | 'cancelled';

// 模擬訂單數據
const mockOrders = [
  {
    id: 1,
    serviceName: 'RAG 檢索增強生成',
    description: '企業知識庫問答系統開發',
    status: 'in_progress' as OrderStatus,
    progress: 65,
    price: 12000,
    createdAt: '2024-01-15',
    estimatedCompletion: '2024-01-25'
  },
  {
    id: 2,
    serviceName: 'Dify 智能平台',
    description: '客戶服務自動化平台',
    status: 'completed' as OrderStatus,
    progress: 100,
    price: 18000,
    createdAt: '2024-01-10',
    estimatedCompletion: '2024-01-20'
  },
  {
    id: 3,
    serviceName: 'AI 視頻處理',
    description: '產品宣傳視頻智能剪輯',
    status: 'pending' as OrderStatus,
    progress: 0,
    price: 8500,
    createdAt: '2024-01-18',
    estimatedCompletion: '2024-01-28'
  },
  {
    id: 4,
    serviceName: '智能客服機器人',
    description: '多語言客戶支持系統',
    status: 'in_progress' as OrderStatus,
    progress: 30,
    price: 15000,
    createdAt: '2024-01-12',
    estimatedCompletion: '2024-01-22'
  }
];

const statusConfig = {
  pending: {
    label: '待開始',
    color: '#f59e0b',
    icon: Clock,
    bgColor: 'bg-yellow-50',
    textColor: 'text-yellow-700'
  },
  in_progress: {
    label: '進行中',
    color: '#3b82f6',
    icon: AlertCircle,
    bgColor: 'bg-blue-50',
    textColor: 'text-blue-700'
  },
  completed: {
    label: '已完成',
    color: '#10b981',
    icon: CheckCircle,
    bgColor: 'bg-green-50',
    textColor: 'text-green-700'
  },
  cancelled: {
    label: '已取消',
    color: '#ef4444',
    icon: XCircle,
    bgColor: 'bg-red-50',
    textColor: 'text-red-700'
  }
};

const OrdersPage: React.FC = () => {
  const navigate = useNavigate();
  const { state } = useAuth();
  const [activeTab, setActiveTab] = useState<'all' | OrderStatus>('all');

  const filteredOrders = activeTab === 'all' 
    ? mockOrders 
    : mockOrders.filter(order => order.status === activeTab);

  const handleOrderClick = (orderId: number) => {
    navigate(`/orders/${orderId}`);
  };

  // 如果未登入，顯示登入提示
  if (!state.isAuthenticated) {
    return (
      <MobileLayout enable3D={true}>
        <div className="flex flex-col h-full bg-gray-50 relative">
          {/* 3D 背景效果 */}
          <View className="absolute inset-0 opacity-10">
            <Suspense fallback={null}>
              <ParticleSystem
                count={100}
                color="#3b82f6"
                size={0.002}
                speed={0.03}
                spread={5}
              />
              <Common />
            </Suspense>
          </View>

          {/* 登入提示內容 */}
          <div className="flex-1 flex items-center justify-center px-6 relative z-10">
            <div className="text-center max-w-sm">
              <div className="w-20 h-20 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <ShoppingBag className="w-10 h-10 text-white" />
              </div>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                查看我的訂單
              </h2>

              <p className="text-gray-600 mb-8 leading-relaxed">
                登入您的帳號以查看訂單記錄、追蹤項目進度和管理服務
              </p>

              <div className="space-y-3">
                <button
                  onClick={() => navigate('/login')}
                  className="w-full bg-gradient-to-r from-primary-600 to-primary-700 text-white py-3 px-6 rounded-lg font-medium hover:from-primary-700 hover:to-primary-800 transition-all"
                >
                  立即登入
                </button>

                <button
                  onClick={() => navigate('/services')}
                  className="w-full border border-primary-600 text-primary-600 py-3 px-6 rounded-lg font-medium hover:bg-primary-50 transition-colors"
                >
                  瀏覽服務
                </button>
              </div>
            </div>
          </div>
        </div>
      </MobileLayout>
    );
  }

  return (
    <MobileLayout enable3D={true}>
      <div className="flex flex-col h-full bg-gray-50 relative">
        {/* 3D 背景效果 */}
        <View className="absolute inset-0 opacity-10">
          <Suspense fallback={null}>
            <ParticleSystem 
              count={300}
              color="#3b82f6"
              size={0.002}
              speed={0.05}
              spread={6}
            />
            <Common />
          </Suspense>
        </View>

        {/* 頭部 */}
        <div className="bg-white px-6 py-4 shadow-sm relative z-10">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">我的訂單</h1>
          
          {/* 狀態篩選標籤 */}
          <div className="flex overflow-x-auto space-x-2 pb-2">
            <button
              onClick={() => setActiveTab('all')}
              className={`px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-colors ${
                activeTab === 'all'
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              全部訂單
            </button>
            {Object.entries(statusConfig).map(([status, config]) => (
              <button
                key={status}
                onClick={() => setActiveTab(status as OrderStatus)}
                className={`px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-colors ${
                  activeTab === status
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {config.label}
              </button>
            ))}
          </div>
        </div>

        {/* 訂單列表 */}
        <div className="flex-1 px-6 py-4 space-y-4 relative z-10">
          {filteredOrders.map((order) => {
            const config = statusConfig[order.status];
            const StatusIcon = config.icon;

            return (
              <div
                key={order.id}
                className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => handleOrderClick(order.id)}
              >
                <div className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-1">
                        {order.serviceName}
                      </h3>
                      <p className="text-sm text-gray-600 mb-2">
                        {order.description}
                      </p>
                      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.bgColor} ${config.textColor}`}>
                        <StatusIcon className="w-3 h-3 mr-1" />
                        {config.label}
                      </div>
                    </div>
                    <Eye className="w-5 h-5 text-gray-400" />
                  </div>

                  {/* 3D 進度指示器 */}
                  {order.status === 'in_progress' && (
                    <div className="h-16 mb-3">
                      <View className="w-full h-full">
                        <Suspense fallback={null}>
                          <Progress3D
                            progress={order.progress}
                            size={0.6}
                            color={config.color}
                            position={[0, 0, 0]}
                            animated={true}
                          />
                          <Common />
                        </Suspense>
                      </View>
                    </div>
                  )}

                  <div className="flex justify-between items-center text-sm text-gray-500">
                    <span>創建時間：{order.createdAt}</span>
                    <span className="font-medium text-gray-900">
                      ¥{order.price.toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}

          {filteredOrders.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-2">
                <Clock className="w-12 h-12 mx-auto" />
              </div>
              <p className="text-gray-500">暫無相關訂單</p>
            </div>
          )}
        </div>

        {/* 底部空白 */}
        <div className="h-4"></div>
      </div>
    </MobileLayout>
  );
};

export default OrdersPage;
