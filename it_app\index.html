<!doctype html>
<html lang="zh-TW">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    
    <!-- 移動端優化 -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="AI速應">
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    
    <!-- SEO優化 -->
    <meta name="description" content="AI速應 - 專業的AI接案服務平台，連接AI需求方與開發者">
    <meta name="keywords" content="AI,人工智能,接案,外包,開發,機器學習,深度學習">
    <meta name="author" content="AI速應團隊">
    
    <!-- Open Graph -->
    <meta property="og:title" content="AI速應 - AI接案服務平台">
    <meta property="og:description" content="專業的AI接案服務平台，為您提供高質量的AI開發服務">
    <meta property="og:type" content="website">
    <meta property="og:image" content="/og-image.png">
    
    <!-- 主題顏色 -->
    <meta name="theme-color" content="#3b82f6">
    <meta name="msapplication-navbutton-color" content="#3b82f6">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    
    <title>AI速應 - AI接案服務平台</title>
    <script src="https://cdn.jsdelivr.net/npm/opencc@1.0.5/dist/opencc.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://unpkg.com/lucide-static/font/lucide.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f7fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        }
        .phone-container {
            width: 390px;
            height: 844px;
            background-color: white;
            border-radius: 40px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            position: relative;
            margin: 20px;
            display: inline-block;
            vertical-align: top;
        }
        .status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            position: relative;
            z-index: 10;
        }
        .status-bar-left {
            display: flex;
            align-items: center;
        }
        .status-bar-right {
            display: flex;
            align-items: center;
        }
        .status-bar-time {
            font-weight: 600;
            font-size: 14px;
        }
        .status-bar-battery {
            width: 25px;
            height: 12px;
            border: 1px solid #000;
            border-radius: 3px;
            padding: 1px;
            position: relative;
            margin-left: 5px;
        }
        .status-bar-battery::after {
            content: '';
            position: absolute;
            top: 3px;
            right: -3px;
            width: 3px;
            height: 4px;
            background-color: #000;
            border-radius: 0 2px 2px 0;
        }
        .status-bar-battery-level {
            height: 100%;
            width: 80%;
            background-color: #000;
            border-radius: 1px;
        }
        .app-frame {
            width: 100%;
            height: calc(100% - 44px - 70px);
            border: none;
        }
        .nav-bar {
            height: 70px;
            background-color: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-around;
            align-items: center;
            position: absolute;
            bottom: 0;
            width: 100%;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #8e8e93;
            font-size: 10px;
        }
        .nav-item.active {
            color: #007aff;
        }
        .nav-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }
        .page-title {
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            margin-top: 10px;
            margin-bottom: 20px;
        }
        .showcase-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
            padding: 20px;
        }
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div class="flex justify-end mb-2">
        <button id="languageToggle" class="px-3 py-1 bg-indigo-100 text-indigo-600 rounded-full text-sm" onclick="toggleSimplifiedTraditional()">
            切换简体
        </button>
        <script>
            function toggleSimplifiedTraditional() {
                const converter = new OpenCC('t2s.json');
                const elements = document.querySelectorAll('body *');
                const currentLang = document.documentElement.lang;
                
                if (currentLang === 'zh-CN') {
                    // 轉換為繁體
                    document.documentElement.lang = 'zh-TW';
                    document.getElementById('languageToggle').textContent = '切换简体';
                    elements.forEach(el => {
                        if (el.textContent && el.children.length === 0) {
                            converter.convert(el.textContent, 't2s').then(result => {
                                el.textContent = result;
                            });
                        }
                    });
                } else {
                    // 轉換為簡體
                    document.documentElement.lang = 'zh-CN';
                    document.getElementById('languageToggle').textContent = '切换繁体';
                    elements.forEach(el => {
                        if (el.textContent && el.children.length === 0) {
                            converter.convert(el.textContent, 's2t').then(result => {
                                el.textContent = result;
                            });
                        }
                    });
                }
                
                // 更新iframe中的內容
                document.querySelectorAll('iframe').forEach(iframe => {
                    iframe.contentWindow.postMessage({
                        type: 'languageChange',
                        lang: document.documentElement.lang,
                        converter: converter
                    }, '*');
                });
            }
            
            // 監聽iframe的語言切換請求
            window.addEventListener('message', function(event) {
                if (event.data.type === 'languageChange') {
                    document.documentElement.lang = event.data.lang;
                    
                    // 同步更新iframe内容
                    const converter = new OpenCC('t2s.json');
                    const elements = document.querySelectorAll('body *');
                    elements.forEach(el => {
                        if (el.textContent && el.children.length === 0) {
                            const direction = event.data.lang === 'zh-CN' ? 's2t' : 't2s';
                            converter.convert(el.textContent, direction).then(result => {
                                el.textContent = result;
                            });
                        }
                    });
                }
            });

            // 初始化iframe語言狀態
            window.addEventListener('load', function() {
                document.querySelectorAll('iframe').forEach(iframe => {
                    iframe.onload = function() {
                        this.contentWindow.postMessage({
                            type: 'initLanguage',
                            lang: document.documentElement.lang
                        }, '*');
                    };
                });
            });

            // 为iframe内嵌页面添加语言转换监听
            document.querySelectorAll('iframe').forEach(iframe => {
                iframe.contentWindow.addEventListener('load', function() {
                    // 初始化iframe内容语言
                    const converter = new OpenCC('t2s.json');
                    const direction = document.documentElement.lang === 'zh-CN' ? 's2t' : 't2s';
                    const elements = this.document.querySelectorAll('body *');
                    elements.forEach(el => {
                        if (el.textContent && el.children.length === 0) {
                            converter.convert(el.textContent, direction).then(result => {
                                el.textContent = result;
                            });
                        }
                    });
                    
                    // 监听语言切换事件
                    this.window.addEventListener('message', function(event) {
                        if (event.data.type === 'languageChange') {
                            const converter = new OpenCC('t2s.json');
                            const direction = event.data.lang === 'zh-CN' ? 's2t' : 't2s';
                            const elements = this.document.querySelectorAll('body *');
                            elements.forEach(el => {
                                if (el.textContent && el.children.length === 0) {
                                    converter.convert(el.textContent, direction).then(result => {
                                        el.textContent = result;
                                    });
                                }
                            });
                        }
                        
                        // 监听iframe内部的语言切换请求
                        if (event.data.type === 'iframeLanguageChange') {
                            const converter = new OpenCC('t2s.json');
                            const direction = document.documentElement.lang === 'zh-CN' ? 's2t' : 't2s';
                            const elements = document.querySelectorAll('body *');
                            elements.forEach(el => {
                                if (el.textContent && el.children.length === 0) {
                                    converter.convert(el.textContent, direction).then(result => {
                                        el.textContent = result;
                                    });
                                }
                            });
                            
                            // 同步更新所有iframe
                            document.querySelectorAll('iframe').forEach(iframe => {
                                iframe.contentWindow.postMessage({
                                    type: 'languageChange',
                                    lang: document.documentElement.lang
                                }, '*');
                            });
                        }
                        if (event.data.type === 'languageChange') {
                            const converter = event.data.converter;
                            const elements = this.document.querySelectorAll('body *');
                            elements.forEach(el => {
                                if (el.textContent && el.children.length === 0) {
                                    const direction = event.data.lang === 'zh-CN' ? 's2t' : 't2s';
                                    converter.convert(el.textContent, direction).then(result => {
                                        el.textContent = result;
                                    });
                                }
                            });
                        }
                    });
                });
            });
        </script>
    </div>
    <h1 class="text-2xl font-bold text-center mb-6">AI速應 - 移動應用界面展示</h1>
    
    <div class="showcase-container">
        <!-- 首页界面 -->
        <div class="phone-container">
            <div class="status-bar">
                <div class="status-bar-left">
                    <span class="status-bar-time">9:41</span>
                </div>
                <div class="status-bar-right">
                    <span class="mr-1">5G</span>
                    <span class="mr-1">100%</span>
                    <div class="status-bar-battery">
                        <div class="status-bar-battery-level"></div>
                    </div>
                </div>
            </div>
            <iframe src="home.html" class="app-frame" title="首页"></iframe>
            <div class="nav-bar">
                <div class="nav-item active">
                    <i class="lucide lucide-home nav-icon"></i>
                    <span>首頁</span>
                </div>
                <div class="nav-item">
                    <i class="lucide lucide-layers nav-icon"></i>
                    <span>分類</span>
                </div>
                <div class="nav-item">
                    <i class="lucide lucide-shopping-cart nav-icon"></i>
                    <span>訂單</span>
                </div>
                <div class="nav-item">
                    <i class="lucide lucide-user nav-icon"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
        
        <!-- 分类界面 -->
        <div class="phone-container">
            <div class="status-bar">
                <div class="status-bar-left">
                    <span class="status-bar-time">9:41</span>
                </div>
                <div class="status-bar-right">
                    <span class="mr-1">5G</span>
                    <span class="mr-1">100%</span>
                    <div class="status-bar-battery">
                        <div class="status-bar-battery-level"></div>
                    </div>
                </div>
            </div>
            <iframe src="category.html" class="app-frame" title="分类"></iframe>
            <div class="nav-bar">
                <div class="nav-item">
                    <i class="lucide lucide-home nav-icon"></i>
                    <span>首頁</span>
                </div>
                <div class="nav-item active">
                    <i class="lucide lucide-layers nav-icon"></i>
                    <span>分類</span>
                </div>
                <div class="nav-item">
                    <i class="lucide lucide-shopping-cart nav-icon"></i>
                    <span>訂單</span>
                </div>
                <div class="nav-item">
                    <i class="lucide lucide-user nav-icon"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
        
        <!-- 订单界面 -->
        <div class="phone-container">
            <div class="status-bar">
                <div class="status-bar-left">
                    <span class="status-bar-time">9:41</span>
                </div>
                <div class="status-bar-right">
                    <span class="mr-1">5G</span>
                    <span class="mr-1">100%</span>
                    <div class="status-bar-battery">
                        <div class="status-bar-battery-level"></div>
                    </div>
                </div>
            </div>
            <iframe src="order.html" class="app-frame" title="订单"></iframe>
            <div class="nav-bar">
                <div class="nav-item">
                    <i class="lucide lucide-home nav-icon"></i>
                    <span>首頁</span>
                </div>
                <div class="nav-item">
                    <i class="lucide lucide-layers nav-icon"></i>
                    <span>分類</span>
                </div>
                <div class="nav-item active">
                    <i class="lucide lucide-shopping-cart nav-icon"></i>
                    <span>訂單</span>
                </div>
                <div class="nav-item">
                    <i class="lucide lucide-user nav-icon"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
        
        <!-- 用户中心界面 -->
        <div class="phone-container">
            <div class="status-bar">
                <div class="status-bar-left">
                    <span class="status-bar-time">9:41</span>
                </div>
                <div class="status-bar-right">
                    <span class="mr-1">5G</span>
                    <span class="mr-1">100%</span>
                    <div class="status-bar-battery">
                        <div class="status-bar-battery-level"></div>
                    </div>
                </div>
            </div>
            <iframe src="profile.html" class="app-frame" title="我的"></iframe>
            <div class="nav-bar">
                <div class="nav-item">
                    <i class="lucide lucide-home nav-icon"></i>
                    <span>首頁</span>
                </div>
                <div class="nav-item">
                    <i class="lucide lucide-layers nav-icon"></i>
                    <span>分類</span>
                </div>
                <div class="nav-item">
                    <i class="lucide lucide-shopping-cart nav-icon"></i>
                    <span>訂單</span>
                </div>
                <div class="nav-item active">
                    <i class="lucide lucide-user nav-icon"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
        
        <!-- 服务详情界面 -->
        <div class="phone-container">
            <div class="status-bar">
                <div class="status-bar-left">
                    <span class="status-bar-time">9:41</span>
                </div>
                <div class="status-bar-right">
                    <span class="mr-1">5G</span>
                    <span class="mr-1">100%</span>
                    <div class="status-bar-battery">
                        <div class="status-bar-battery-level"></div>
                    </div>
                </div>
            </div>
            <iframe src="dify-service.html" class="app-frame" title="服务详情"></iframe>
            <div class="nav-bar">
                <div class="nav-item">
                    <i class="lucide lucide-home nav-icon"></i>
                    <span>首頁</span>
                </div>
                <div class="nav-item active">
                    <i class="lucide lucide-layers nav-icon"></i>
                    <span>分類</span>
                </div>
                <div class="nav-item">
                    <i class="lucide lucide-shopping-cart nav-icon"></i>
                    <span>訂單</span>
                </div>
                <div class="nav-item">
                    <i class="lucide lucide-user nav-icon"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
        
        <!-- RAG服务界面 -->
        <div class="phone-container">
            <div class="status-bar">
                <div class="status-bar-left">
                    <span class="status-bar-time">9:41</span>
                </div>
                <div class="status-bar-right">
                    <span class="mr-1">5G</span>
                    <span class="mr-1">100%</span>
                    <div class="status-bar-battery">
                        <div class="status-bar-battery-level"></div>
                    </div>
                </div>
            </div>
            <iframe src="rag-service.html" class="app-frame" title="RAG服务"></iframe>
            <div class="nav-bar">
                <div class="nav-item">
                    <i class="lucide lucide-home nav-icon"></i>
                    <span>首頁</span>
                </div>
                <div class="nav-item active">
                    <i class="lucide lucide-layers nav-icon"></i>
                    <span>分類</span>
                </div>
                <div class="nav-item">
                    <i class="lucide lucide-shopping-cart nav-icon"></i>
                    <span>訂單</span>
                </div>
                <div class="nav-item">
                    <i class="lucide lucide-user nav-icon"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
        
        <!-- AgenticRAG服务界面 -->
        <div class="phone-container">
            <div class="status-bar">
                <div class="status-bar-left">
                    <span class="status-bar-time">9:41</span>
                </div>
                <div class="status-bar-right">
                    <span class="mr-1">5G</span>
                    <span class="mr-1">100%</span>
                    <div class="status-bar-battery">
                        <div class="status-bar-battery-level"></div>
                    </div>
                </div>
            </div>
            <iframe src="agentic-rag-service.html" class="app-frame" title="AgenticRAG服务"></iframe>
            <div class="nav-bar">
                <div class="nav-item">
                    <i class="lucide lucide-home nav-icon"></i>
                    <span>首頁</span>
                </div>
                <div class="nav-item active">
                    <i class="lucide lucide-layers nav-icon"></i>
                    <span>分類</span>
                </div>
                <div class="nav-item">
                    <i class="lucide lucide-shopping-cart nav-icon"></i>
                    <span>訂單</span>
                </div>
                <div class="nav-item">
                    <i class="lucide lucide-user nav-icon"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
        <!-- AI视频服务界面 -->
        <div class="phone-container">
            <div class="status-bar">
                <div class="status-bar-left">
                    <span class="status-bar-time">9:41</span>
                </div>
                <div class="status-bar-right">
                    <span class="mr-1">5G</span>
                    <span class="mr-1">100%</span>
                    <div class="status-bar-battery">
                        <div class="status-bar-battery-level"></div>
                    </div>
                </div>
            </div>
            <iframe src="ai-video-service.html" class="app-frame" title="AI视频服务"></iframe>
            <div class="nav-bar">
                <div class="nav-item">
                    <i class="lucide lucide-home nav-icon"></i>
                    <span>首頁</span>
                </div>
                <div class="nav-item active">
                    <i class="lucide lucide-layers nav-icon"></i>
                    <span>分類</span>
                </div>
                <div class="nav-item">
                    <i class="lucide lucide-shopping-cart nav-icon"></i>
                    <span>訂單</span>
                </div>
                <div class="nav-item">
                    <i class="lucide lucide-user nav-icon"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
    </div>
<script>
    const toggleBtn = document.getElementById('languageToggle');
    const iframes = document.querySelectorAll('.app-frame');
    
    toggleBtn.addEventListener('click', () => {
        const isTraditional = toggleBtn.textContent.includes('简体');
        
        // 切换按钮文本
        toggleBtn.textContent = isTraditional ? '切换繁體' : '切换简体';
        
        // 转换当前页面文本
        convertPageText(isTraditional);
        
        // 转换iframe中的文本
        iframes.forEach(iframe => {
            iframe.contentWindow.postMessage({
                type: 'languageToggle',
                toSimplified: isTraditional
            }, '*');
        });
    });
    
    function convertPageText(toSimplified) {
        const walker = document.createTreeWalker(
            document.body, 
            NodeFilter.SHOW_TEXT, 
            null, 
            false
        );
        
        let node;
        while (node = walker.nextNode()) {
            if (node.nodeValue.trim()) {
                node.nodeValue = toSimplified 
                    ? OpenCC.convert(node.nodeValue, 't2s.json')
                    : OpenCC.convert(node.nodeValue, 's2t.json');
            }
        }
    }
    
    // 监听iframe中的语言切换请求
    window.addEventListener('message', (event) => {
        if (event.data.type === 'languageToggle') {
            convertPageText(event.data.toSimplified);
        }
    });
</script>
</body>
</html>