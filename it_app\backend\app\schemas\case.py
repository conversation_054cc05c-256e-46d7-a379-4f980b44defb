"""
AI 案例相關 Pydantic 模型
"""

from decimal import Decimal
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field


class CaseTestimonialResponse(BaseModel):
    """案例評價響應模型"""
    
    id: int = Field(..., description="評價ID")
    client_name: str = Field(..., description="客戶姓名")
    client_title: Optional[str] = Field(None, description="客戶職位")
    client_company: Optional[str] = Field(None, description="客戶公司")
    client_avatar: Optional[str] = Field(None, description="客戶頭像URL")
    content: str = Field(..., description="評價內容")
    rating: Decimal = Field(..., description="評分")
    is_featured: bool = Field(..., description="是否為精選評價")
    created_at: datetime = Field(..., description="創建時間")
    
    class Config:
        from_attributes = True


class AICaseListResponse(BaseModel):
    """AI 案例列表響應模型"""
    
    id: int = Field(..., description="案例ID")
    title: str = Field(..., description="案例標題")
    subtitle: Optional[str] = Field(None, description="案例副標題")
    description: str = Field(..., description="案例簡介")
    technologies: Optional[List[str]] = Field(None, description="使用的AI技術")
    category: str = Field(..., description="案例分類")
    industry: Optional[str] = Field(None, description="所屬行業")
    client_name: Optional[str] = Field(None, description="客戶名稱")
    project_duration: Optional[str] = Field(None, description="項目週期")
    cover_image: Optional[str] = Field(None, description="封面圖片URL")
    client_rating: Optional[Decimal] = Field(None, description="客戶評分")
    is_featured: bool = Field(..., description="是否為精選案例")
    view_count: int = Field(..., description="瀏覽次數")
    created_at: datetime = Field(..., description="創建時間")
    
    class Config:
        from_attributes = True


class AICaseDetailResponse(BaseModel):
    """AI 案例詳情響應模型"""
    
    id: int = Field(..., description="案例ID")
    title: str = Field(..., description="案例標題")
    subtitle: Optional[str] = Field(None, description="案例副標題")
    description: str = Field(..., description="案例簡介")
    detailed_description: Optional[str] = Field(None, description="詳細描述")
    technologies: Optional[List[str]] = Field(None, description="使用的AI技術")
    category: str = Field(..., description="案例分類")
    industry: Optional[str] = Field(None, description="所屬行業")
    
    # 項目信息
    client_name: Optional[str] = Field(None, description="客戶名稱")
    project_duration: Optional[str] = Field(None, description="項目週期")
    project_budget: Optional[Decimal] = Field(None, description="項目預算")
    
    # 成果信息
    achievements: Optional[List[str]] = Field(None, description="項目成果")
    benefits: Optional[str] = Field(None, description="項目效益")
    roi_percentage: Optional[Decimal] = Field(None, description="投資回報率")
    
    # 媒體信息
    cover_image: Optional[str] = Field(None, description="封面圖片URL")
    gallery_images: Optional[List[str]] = Field(None, description="案例圖片庫")
    demo_video: Optional[str] = Field(None, description="演示視頻URL")
    
    # 評價信息
    client_rating: Optional[Decimal] = Field(None, description="客戶評分")
    is_featured: bool = Field(..., description="是否為精選案例")
    view_count: int = Field(..., description="瀏覽次數")
    
    # 客戶評價
    testimonials: List[CaseTestimonialResponse] = Field(default=[], description="客戶評價列表")
    
    # 時間信息
    created_at: datetime = Field(..., description="創建時間")
    updated_at: datetime = Field(..., description="更新時間")
    
    class Config:
        from_attributes = True


class CaseStatsResponse(BaseModel):
    """案例統計響應模型"""
    
    total_cases: int = Field(..., description="總案例數")
    featured_cases: int = Field(..., description="精選案例數")
    total_views: int = Field(..., description="總瀏覽量")
    average_rating: Optional[Decimal] = Field(None, description="平均評分")
    categories: List[dict] = Field(..., description="分類統計")
    technologies: List[dict] = Field(..., description="技術統計")


class CaseCategoryResponse(BaseModel):
    """案例分類響應模型"""
    
    category: str = Field(..., description="分類名稱")
    count: int = Field(..., description="案例數量")
    display_name: str = Field(..., description="顯示名稱")


class CaseTechnologyResponse(BaseModel):
    """案例技術響應模型"""
    
    technology: str = Field(..., description="技術名稱")
    count: int = Field(..., description="使用次數")
    display_name: str = Field(..., description="顯示名稱")
