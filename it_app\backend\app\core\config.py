"""
應用程序配置設置
使用 Pydantic Settings 管理環境變量
"""

from typing import List, Optional
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """應用程序設置類"""
    
    # 應用基本配置
    app_name: str = Field(default="AI速應後端API", description="應用名稱")
    app_version: str = Field(default="1.0.0", description="應用版本")
    debug: bool = Field(default=False, description="調試模式")
    environment: str = Field(default="production", description="運行環境")
    
    # 服務器配置
    host: str = Field(default="0.0.0.0", description="服務器主機")
    port: int = Field(default=8000, description="服務器端口")
    
    # 數據庫配置
    database_url: str = Field(
        default="sqlite+aiosqlite:///./app.db",
        description="數據庫連接URL"
    )
    
    # JWT 配置
    secret_key: str = Field(
        default="your-super-secret-key-change-this-in-production",
        description="JWT 密鑰"
    )
    algorithm: str = Field(default="HS256", description="JWT 算法")
    access_token_expire_minutes: int = Field(
        default=60, 
        description="訪問令牌過期時間（分鐘）"
    )
    refresh_token_expire_days: int = Field(
        default=7, 
        description="刷新令牌過期時間（天）"
    )
    
    # 密碼加密配置
    bcrypt_rounds: int = Field(default=12, description="bcrypt 加密輪數")
    
    # CORS 配置
    allowed_origins: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        description="允許的跨域來源"
    )
    
    # Email 配置
    smtp_host: str = Field(default="smtp.gmail.com", description="SMTP 服務器")
    smtp_port: int = Field(default=587, description="SMTP 端口")
    smtp_username: Optional[str] = Field(default=None, description="SMTP 用戶名")
    smtp_password: Optional[str] = Field(default=None, description="SMTP 密碼")
    email_from: Optional[str] = Field(default=None, description="發件人郵箱")
    
    # LINE 登入配置
    line_channel_id: Optional[str] = Field(default=None, description="LINE Channel ID")
    line_channel_secret: Optional[str] = Field(default=None, description="LINE Channel Secret")
    line_redirect_uri: str = Field(
        default="http://localhost:8000/api/auth/line/callback",
        description="LINE 重定向 URI"
    )
    
    # 速率限制配置
    rate_limit_per_minute: int = Field(default=100, description="每分鐘請求限制")
    
    # 日誌配置
    log_level: str = Field(default="INFO", description="日誌級別")
    log_format: str = Field(default="json", description="日誌格式")
    
    @validator("allowed_origins", pre=True)
    def parse_cors_origins(cls, v):
        """解析 CORS 來源列表"""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("secret_key")
    def validate_secret_key(cls, v):
        """驗證密鑰強度"""
        if len(v) < 32:
            raise ValueError("Secret key must be at least 32 characters long")
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# 創建全局設置實例
settings = Settings()


def get_settings() -> Settings:
    """獲取應用設置實例"""
    return settings
