{"name": "ai-case-mobile-app", "private": true, "version": "1.0.0", "type": "module", "description": "AI接案移動端應用", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "three": "^0.176.0", "@react-three/fiber": "^8.15.12", "@react-three/drei": "^9.92.7", "tunnel-rat": "^0.1.2", "three-custom-shader-material": "^6.3.5"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/three": "^0.176.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8"}}