import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { Mail, ArrowLeft, CheckCircle, AlertCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import MobileLayout from '@/components/layout/MobileLayout';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';

const EmailLoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { login } = useAuth();
  const [step, setStep] = useState<'email' | 'verification'>('email');
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [countdown, setCountdown] = useState(0);

  const handleSendCode = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) {
      setError('請輸入郵箱地址');
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('請輸入有效的郵箱地址');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // 模擬發送驗證碼 API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setStep('verification');
      setCountdown(60);
      
      // 倒計時
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      
    } catch (error) {
      setError('發送驗證碼失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyCode = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!verificationCode.trim()) {
      setError('請輸入驗證碼');
      return;
    }

    if (verificationCode.length !== 6) {
      setError('驗證碼應為6位數字');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // 模擬驗證碼驗證 API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模擬登錄成功
      await login({
        username: email,
        password: 'email_login_' + verificationCode
      });
      
    } catch (error) {
      setError('驗證碼錯誤，請重試');
    } finally {
      setLoading(false);
    }
  };

  const handleResendCode = () => {
    if (countdown > 0) return;
    
    setCountdown(60);
    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  return (
    <MobileLayout showNavBar={false}>
      <div className="flex flex-col h-full bg-gradient-to-br from-blue-50 to-white">
        {/* 頭部區域 */}
        <div className="flex-shrink-0 px-6 pt-8 pb-4">
          <div className="flex items-center mb-6">
            <button
              onClick={() => step === 'email' ? navigate('/login') : setStep('email')}
              className="mr-4 p-2 rounded-full hover:bg-gray-100 transition-colors"
            >
              <ArrowLeft className="w-6 h-6 text-gray-600" />
            </button>
            <h1 className="text-2xl font-bold text-gray-900">
              Email 快速登錄
            </h1>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Mail className="w-8 h-8 text-blue-600" />
            </div>
            <p className="text-gray-600">
              {step === 'email' 
                ? '輸入您的郵箱地址，我們將發送驗證碼' 
                : '請輸入發送到您郵箱的6位驗證碼'
              }
            </p>
          </div>
        </div>

        {/* 主要內容區域 */}
        <div className="flex-1 px-6 py-4">
          <div className="bg-white rounded-2xl shadow-lg p-6">
            {/* 錯誤提示 */}
            {error && (
              <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start">
                <AlertCircle className="w-5 h-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                <span className="text-red-700 text-sm">{error}</span>
              </div>
            )}

            {step === 'email' ? (
              /* 郵箱輸入步驟 */
              <form onSubmit={handleSendCode} className="space-y-6">
                <Input
                  type="email"
                  placeholder="請輸入您的郵箱地址"
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value);
                    setError('');
                  }}
                  leftIcon={<Mail />}
                  required
                />

                <Button
                  type="submit"
                  className="w-full"
                  loading={loading}
                  size="lg"
                >
                  發送驗證碼
                </Button>
              </form>
            ) : (
              /* 驗證碼輸入步驟 */
              <form onSubmit={handleVerifyCode} className="space-y-6">
                <div>
                  <div className="text-center mb-4">
                    <p className="text-sm text-gray-600">
                      驗證碼已發送至
                    </p>
                    <p className="font-medium text-gray-900">{email}</p>
                  </div>
                  
                  <Input
                    type="text"
                    placeholder="請輸入6位驗證碼"
                    value={verificationCode}
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, '').slice(0, 6);
                      setVerificationCode(value);
                      setError('');
                    }}
                    leftIcon={<CheckCircle />}
                    maxLength={6}
                    required
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full"
                  loading={loading}
                  size="lg"
                  disabled={verificationCode.length !== 6}
                >
                  驗證並登錄
                </Button>

                <div className="text-center">
                  <button
                    type="button"
                    onClick={handleResendCode}
                    disabled={countdown > 0}
                    className={`text-sm ${
                      countdown > 0 
                        ? 'text-gray-400 cursor-not-allowed' 
                        : 'text-blue-600 hover:text-blue-700'
                    }`}
                  >
                    {countdown > 0 ? `重新發送 (${countdown}s)` : '重新發送驗證碼'}
                  </button>
                </div>
              </form>
            )}

            {/* 返回普通登錄 */}
            <div className="mt-6 text-center">
              <Link 
                to="/login" 
                className="text-sm text-gray-600 hover:text-gray-800"
              >
                返回普通登錄
              </Link>
            </div>
          </div>
        </div>
      </div>
    </MobileLayout>
  );
};

export default EmailLoginPage;
