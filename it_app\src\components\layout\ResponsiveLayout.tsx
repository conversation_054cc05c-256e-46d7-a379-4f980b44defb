import React, { ReactNode, useRef, Suspense, useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import clsx from 'clsx';
import Scene from '@/components/canvas/Scene';
import ResponsiveNavigation from './ResponsiveNavigation';
import PerformanceWarning from '@/components/ui/PerformanceWarning';
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor';

interface ResponsiveLayoutProps {
  children: ReactNode;
  showNavBar?: boolean;
  enable3D?: boolean;
}

const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  children,
  showNavBar = true,
  enable3D = false
}) => {
  const location = useLocation();
  const ref = useRef<HTMLDivElement>(null);
  const [isMobile, setIsMobile] = useState(false);
  const { adjustSettings } = usePerformanceMonitor();

  // 檢測設備類型
  useEffect(() => {
    const checkDevice = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
    };

    checkDevice();
    window.addEventListener('resize', checkDevice);
    return () => window.removeEventListener('resize', checkDevice);
  }, []);

  // 獲取當前時間（僅移動端顯示）
  const getCurrentTime = () => {
    const now = new Date();
    return now.toLocaleTimeString('zh-TW', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  };

  // 移動端布局
  if (isMobile) {
    return (
      <div className="min-h-screen bg-gray-100 flex justify-center items-center p-4">
        {/* 性能警告 */}
        <PerformanceWarning onOptimize={adjustSettings} />
        
        {/* 手機框架 */}
        <div
          ref={ref}
          className="w-full max-w-mobile h-mobile bg-white rounded-[40px] overflow-hidden shadow-2xl relative"
          style={{
            position: 'relative',
            width: '100%',
            height: '100%',
            overflow: 'auto',
            touchAction: 'auto',
          }}
        >
          
          {/* 手機狀態欄 */}
          <div className="h-11 bg-white flex justify-between items-center px-5 relative z-10">
            {/* 左側：時間 */}
            <div className="flex items-center">
              <span className="text-sm font-semibold text-black">
                {getCurrentTime()}
              </span>
            </div>
            
            {/* 右側：信號、WiFi、電池 */}
            <div className="flex items-center space-x-1">
              {/* 信號強度 */}
              <div className="flex items-center space-x-0.5">
                <div className="w-1 h-1 bg-black rounded-full"></div>
                <div className="w-1 h-2 bg-black rounded-full"></div>
                <div className="w-1 h-3 bg-black rounded-full"></div>
                <div className="w-1 h-4 bg-black rounded-full"></div>
              </div>
              
              {/* WiFi圖標 */}
              <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
                <path d="M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.07 2.93 1 9zm8 8l3 3 3-3c-1.65-1.65-4.34-1.65-6 0zm-4-4l2 2c2.76-2.76 7.24-2.76 10 0l2-2C15.14 9.14 8.87 9.14 5 13z"/>
              </svg>
              
              {/* 電池圖標 */}
              <div className="relative">
                <div className="w-6 h-3 border border-black rounded-sm flex items-center">
                  <div className="w-4/5 h-2 bg-black rounded-sm mx-0.5"></div>
                </div>
                <div className="absolute -right-1 top-0.5 w-1 h-1.5 bg-black rounded-r-sm"></div>
              </div>
            </div>
          </div>

          {/* 主要內容區域 */}
          <div className={clsx(
            "flex-1 overflow-hidden",
            showNavBar ? "h-[calc(100%-44px-70px)]" : "h-[calc(100%-44px)]"
          )}>
            <div className="h-full overflow-y-auto">
              {children}
            </div>
          </div>

          {/* 移動端底部導航 */}
          {showNavBar && (
            <ResponsiveNavigation className="absolute bottom-0 w-full" />
          )}

          {/* 3D Scene 背景 */}
          {enable3D && (
            <Suspense fallback={null}>
              <Scene
                style={{
                  position: 'fixed',
                  top: 0,
                  left: 0,
                  width: '100vw',
                  height: '100vh',
                  pointerEvents: 'none',
                  zIndex: -1,
                }}
                eventSource={ref}
                eventPrefix='client'
              />
            </Suspense>
          )}
        </div>
      </div>
    );
  }

  // 桌面端布局
  return (
    <div className="min-h-screen bg-gray-50">
      {/* 性能警告 */}
      <PerformanceWarning onOptimize={adjustSettings} />
      
      {/* 桌面端頂部導航 */}
      {showNavBar && <ResponsiveNavigation />}
      
      {/* 主要內容區域 */}
      <main 
        ref={ref}
        className={clsx(
          "relative",
          showNavBar ? "min-h-[calc(100vh-64px)]" : "min-h-screen"
        )}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {children}
        </div>

        {/* 3D Scene 背景 */}
        {enable3D && (
          <Suspense fallback={null}>
            <Scene
              style={{
                position: 'fixed',
                top: 0,
                left: 0,
                width: '100vw',
                height: '100vh',
                pointerEvents: 'none',
                zIndex: -1,
              }}
              eventSource={ref}
              eventPrefix='client'
            />
          </Suspense>
        )}
      </main>
    </div>
  );
};

export default ResponsiveLayout;
