# AI速應 - AI接案移動端應用

## 項目概述

AI速應是一個專業的AI接案服務平台移動端應用，旨在連接有AI定制化開發需求的用戶和接案方，提供便捷的案件需求提交、溝通協作、代碼交付與支付結算等一站式服務。

## 技術架構

### 前端技術棧
- **React 18** - 現代化的前端框架
- **TypeScript** - 類型安全的JavaScript超集
- **Vite** - 快速的構建工具
- **Tailwind CSS** - 實用優先的CSS框架
- **React Router** - 客戶端路由管理
- **Axios** - HTTP客戶端
- **Lucide React** - 現代化圖標庫

### 項目結構
```
src/
├── components/          # 可重用UI組件
│   ├── layout/         # 布局組件
│   └── ui/             # 基礎UI組件
├── pages/              # 頁面組件
├── contexts/           # React Context狀態管理
├── services/           # API服務層
├── types/              # TypeScript類型定義
├── hooks/              # 自定義React Hooks
└── utils/              # 工具函數
```

## 核心功能

### 已實現功能
1. **用戶認證系統**
   - 用戶登錄/註冊
   - 第三方登錄（微信、QQ）
   - JWT令牌管理
   - 路由守護

2. **移動端優化布局**
   - 手機框架模擬
   - 狀態欄顯示
   - 底部導航欄
   - 響應式設計

3. **首頁功能**
   - 服務類型展示
   - 成功案例展示
   - 搜索功能
   - 快速操作入口

4. **UI組件庫**
   - 通用按鈕組件
   - 輸入框組件
   - 移動端布局組件

### 待實現功能
- 案件管理系統
- 實時聊天功能
- 支付系統集成
- 個人中心
- 文件上傳功能
- 推送通知

## 設計原則

本項目嚴格遵循以下設計原則：

1. **模塊化設計** - 將功能拆分為獨立、可重用的組件
2. **第一性原理** - 在分析問題和技術架構時追求本質
3. **DRY原則** - 避免重複代碼
4. **KISS原則** - 保持簡單設計
5. **SOLID原則** - 面向對象設計原則
6. **YAGNI原則** - 只實現當前需要的功能

## 安裝和運行

### 環境要求
- Node.js 18+
- npm 或 pnpm

### 安裝依賴
```bash
npm install
# 或
pnpm install
```

### 開發模式運行
```bash
npm run dev
# 或
pnpm dev
```

### 構建生產版本
```bash
npm run build
# 或
pnpm build
```

### 預覽生產版本
```bash
npm run preview
# 或
pnpm preview
```

## 移動端特性

### 響應式設計
- 針對390px寬度的移動設備優化
- 支持不同屏幕尺寸適配
- 觸摸友好的交互設計

### 性能優化
- 代碼分割和懶加載
- 圖片優化
- 緩存策略
- 最小化包大小

### 用戶體驗
- 流暢的動畫效果
- 觸摸反饋
- 加載狀態指示
- 錯誤處理機制

## API集成

### 後端接口
項目設計了完整的RESTful API接口，包括：
- 用戶認證相關接口
- 案件管理接口
- 消息通信接口
- 支付相關接口
- 文件上傳接口

### 狀態管理
使用React Context + useReducer進行狀態管理：
- AuthContext - 用戶認證狀態
- CaseContext - 案件管理狀態（待實現）
- ChatContext - 聊天狀態（待實現）

## 開發規範

### 代碼風格
- 使用TypeScript進行類型檢查
- 遵循ESLint規則
- 統一的命名約定
- 詳細的註釋說明

### 組件設計
- 單一職責原則
- Props類型定義
- 錯誤邊界處理
- 可訪問性支持

### 文件組織
- 按功能模塊組織
- 清晰的導入/導出
- 統一的文件命名

## 部署說明

### 環境變量
創建 `.env` 文件配置環境變量：
```
VITE_API_BASE_URL=http://localhost:8000/api
```

### 構建配置
項目使用Vite進行構建，支持：
- 代碼壓縮
- 資源優化
- Source Map生成
- 瀏覽器兼容性

## 貢獻指南

1. Fork項目
2. 創建功能分支
3. 提交更改
4. 推送到分支
5. 創建Pull Request

## 許可證

本項目採用MIT許可證。

## 聯繫方式

如有問題或建議，請聯繫開發團隊。