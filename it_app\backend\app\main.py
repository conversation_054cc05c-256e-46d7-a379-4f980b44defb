"""
FastAPI 主應用程序
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import time
import logging

from app.core.config import get_settings
from app.core.database import init_db, close_db
from app.api import api_router

settings = get_settings()

# 配置日誌
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """應用程序生命週期管理"""
    # 啟動時執行
    logger.info("正在啟動 AI速應後端 API...")
    
    # 初始化數據庫
    await init_db()
    logger.info("數據庫初始化完成")
    
    # 初始化示例數據
    await init_sample_data()
    logger.info("示例數據初始化完成")
    
    yield
    
    # 關閉時執行
    logger.info("正在關閉 AI速應後端 API...")
    await close_db()
    logger.info("數據庫連接已關閉")


# 創建 FastAPI 應用實例
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="AI接案平台後端API系統",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    lifespan=lifespan
)

# 添加 CORS 中間件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加受信任主機中間件（生產環境）
if not settings.debug:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["localhost", "127.0.0.1", "*.yourdomain.com"]
    )


# 請求處理時間中間件
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """添加請求處理時間頭部"""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


# 全局異常處理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP 異常處理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "data": None
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用異常處理器"""
    logger.error(f"未處理的異常: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "success": False,
            "message": "服務器內部錯誤" if not settings.debug else str(exc),
            "data": None
        }
    )


# 註冊 API 路由
app.include_router(api_router)


# 健康檢查端點
@app.get("/health")
async def health_check():
    """健康檢查"""
    return {
        "status": "healthy",
        "app_name": settings.app_name,
        "version": settings.app_version,
        "environment": settings.environment
    }


# 根路徑
@app.get("/")
async def root():
    """根路徑"""
    return {
        "message": f"歡迎使用 {settings.app_name}",
        "version": settings.app_version,
        "docs": "/docs" if settings.debug else "文檔已在生產環境中禁用"
    }


async def init_sample_data():
    """初始化示例數據"""
    from app.core.database import AsyncSessionLocal
    from app.models.service import AIService
    from app.models.case import AICase, CaseTestimonial
    from sqlalchemy import select
    from decimal import Decimal

    async with AsyncSessionLocal() as db:
        # 檢查是否已有服務數據
        stmt = select(AIService)
        result = await db.execute(stmt)
        existing_services = result.scalars().all()

        if not existing_services:
            # 創建示例 AI 服務
            sample_services = [
            AIService(
                name="RAG 檢索增強生成",
                description="智能文檔問答系統，支持企業知識庫檢索和智能問答",
                category="ai-search",
                price_min=5000,
                price_max=15000
            ),
            AIService(
                name="Dify 智能平台",
                description="低代碼AI應用開發平台，快速構建智能應用",
                category="ai-platform",
                price_min=8000,
                price_max=25000
            ),
            AIService(
                name="AI 視頻處理",
                description="智能視頻分析與生成，支持自動剪輯和內容識別",
                category="ai-media",
                price_min=10000,
                price_max=30000
            ),
            AIService(
                name="智能客服機器人",
                description="24/7 自動客戶服務，支持多語言和情感分析",
                category="ai-chat",
                price_min=3000,
                price_max=12000
            ),
            AIService(
                name="圖像識別系統",
                description="物體檢測與分類，支持實時圖像分析",
                category="ai-vision",
                price_min=6000,
                price_max=20000
            ),
            AIService(
                name="數據分析平台",
                description="智能數據洞察，自動生成分析報告",
                category="ai-analytics",
                price_min=7000,
                price_max=22000
            )
        ]
        
        for service in sample_services:
            db.add(service)
        
            await db.commit()
            logger.info(f"已創建 {len(sample_services)} 個示例 AI 服務")

        # 檢查是否已有案例數據
        case_stmt = select(AICase)
        case_result = await db.execute(case_stmt)
        existing_cases = case_result.scalars().all()

        if not existing_cases:
            # 創建示例 AI 案例
            sample_cases = [
                AICase(
                    title="智慧醫療診斷系統",
                    subtitle="基於深度學習的醫學影像分析平台",
                    description="為某三甲醫院開發的AI輔助診斷系統，能夠快速準確地分析醫學影像，提高診斷效率和準確性。",
                    detailed_description="該系統採用最新的深度學習技術，結合大量醫學影像數據訓練，能夠識別多種疾病特徵。系統部署後，診斷準確率提升至95%以上，診斷時間縮短70%。",
                    technologies=["Deep Learning", "Computer Vision", "TensorFlow", "Medical AI"],
                    category="ai-vision",
                    industry="醫療健康",
                    client_name="某三甲醫院",
                    project_duration="6個月",
                    project_budget=Decimal("500000"),
                    achievements=["診斷準確率提升至95%", "診斷時間縮短70%", "減少誤診率60%"],
                    benefits="大幅提升醫院診斷效率，減少醫生工作負擔，提高患者滿意度。預計每年可節省成本200萬元。",
                    roi_percentage=Decimal("400.00"),
                    cover_image="https://example.com/images/medical-ai.jpg",
                    gallery_images=["https://example.com/gallery/medical-1.jpg", "https://example.com/gallery/medical-2.jpg"],
                    client_rating=Decimal("4.95"),
                    is_featured=True,
                    view_count=1250
                ),
                AICase(
                    title="智能客服機器人",
                    subtitle="24/7全天候智能客戶服務解決方案",
                    description="為大型電商平台開發的智能客服系統，支持多輪對話、情感分析和自動問題解決。",
                    detailed_description="系統集成了自然語言處理、知識圖譜和機器學習技術，能夠理解客戶意圖並提供準確回答。支持文字、語音多種交互方式。",
                    technologies=["NLP", "ChatGPT", "LangChain", "Knowledge Graph"],
                    category="ai-chat",
                    industry="電子商務",
                    client_name="某大型電商平台",
                    project_duration="4個月",
                    project_budget=Decimal("300000"),
                    achievements=["客戶滿意度提升85%", "人工客服成本降低60%", "響應時間縮短至秒級"],
                    benefits="顯著提升客戶體驗，降低運營成本，提高服務效率。",
                    roi_percentage=Decimal("320.00"),
                    cover_image="https://example.com/images/chatbot.jpg",
                    client_rating=Decimal("4.88"),
                    is_featured=True,
                    view_count=980
                ),
                AICase(
                    title="企業知識管理平台",
                    subtitle="基於RAG技術的智能知識檢索系統",
                    description="為大型企業構建的智能知識管理平台，實現企業文檔的智能檢索和問答。",
                    detailed_description="採用RAG（檢索增強生成）技術，結合向量數據庫和大語言模型，為企業提供智能化的知識管理解決方案。",
                    technologies=["RAG", "Vector Database", "LangChain", "Elasticsearch"],
                    category="ai-search",
                    industry="企業服務",
                    client_name="某大型製造企業",
                    project_duration="5個月",
                    project_budget=Decimal("400000"),
                    achievements=["知識檢索效率提升90%", "員工培訓時間縮短50%", "知識利用率提升200%"],
                    benefits="大幅提升企業知識管理效率，促進知識共享和創新。",
                    roi_percentage=Decimal("280.00"),
                    cover_image="https://example.com/images/knowledge-platform.jpg",
                    client_rating=Decimal("4.92"),
                    is_featured=True,
                    view_count=756
                ),
                AICase(
                    title="智能視頻內容分析",
                    subtitle="AI驅動的視頻內容理解與處理平台",
                    description="為媒體公司開發的智能視頻分析系統，自動識別內容、生成標籤和摘要。",
                    detailed_description="系統能夠自動分析視頻內容，識別人物、場景、動作等元素，生成智能標籤和內容摘要，大幅提升內容管理效率。",
                    technologies=["Computer Vision", "Video Analysis", "PyTorch", "AI Media"],
                    category="ai-media",
                    industry="媒體娛樂",
                    client_name="某知名媒體公司",
                    project_duration="7個月",
                    project_budget=Decimal("600000"),
                    achievements=["內容處理效率提升80%", "標籤準確率達92%", "人工成本降低65%"],
                    benefits="革命性地改變了視頻內容管理流程，提升了內容發現和推薦效果。",
                    roi_percentage=Decimal("350.00"),
                    cover_image="https://example.com/images/video-ai.jpg",
                    client_rating=Decimal("4.85"),
                    is_featured=False,
                    view_count=634
                ),
                AICase(
                    title="智能數據分析平台",
                    subtitle="自動化商業智能分析解決方案",
                    description="為金融機構開發的智能數據分析平台，提供自動化的數據洞察和預測分析。",
                    detailed_description="平台集成了機器學習算法和統計分析模型，能夠自動發現數據模式，生成商業洞察報告，支持決策制定。",
                    technologies=["Machine Learning", "Data Analytics", "Python", "Business Intelligence"],
                    category="ai-analytics",
                    industry="金融服務",
                    client_name="某大型銀行",
                    project_duration="8個月",
                    project_budget=Decimal("800000"),
                    achievements=["分析效率提升75%", "預測準確率達88%", "決策速度提升60%"],
                    benefits="顯著提升了數據分析能力和決策質量，為業務增長提供強有力支持。",
                    roi_percentage=Decimal("420.00"),
                    cover_image="https://example.com/images/data-analytics.jpg",
                    client_rating=Decimal("4.90"),
                    is_featured=True,
                    view_count=892
                ),
                AICase(
                    title="智能推薦系統",
                    subtitle="個性化內容推薦引擎",
                    description="為在線教育平台開發的智能推薦系統，提供個性化的學習內容推薦。",
                    detailed_description="系統基於用戶行為分析和內容特徵提取，使用協同過濾和深度學習技術，為每個用戶提供個性化的學習路徑推薦。",
                    technologies=["Recommendation System", "Collaborative Filtering", "Deep Learning", "User Behavior Analysis"],
                    category="ai-recommendation",
                    industry="在線教育",
                    client_name="某在線教育平台",
                    project_duration="4個月",
                    project_budget=Decimal("250000"),
                    achievements=["用戶參與度提升65%", "學習完成率提升40%", "用戶滿意度提升70%"],
                    benefits="大幅提升了學習效果和用戶體驗，增加了平台粘性和收入。",
                    roi_percentage=Decimal("260.00"),
                    cover_image="https://example.com/images/recommendation.jpg",
                    client_rating=Decimal("4.78"),
                    is_featured=False,
                    view_count=543
                )
            ]

            for case in sample_cases:
                db.add(case)

            await db.commit()

            # 為部分案例添加客戶評價
            testimonials = [
                CaseTestimonial(
                    case_id=1,  # 智慧醫療診斷系統
                    client_name="張主任",
                    client_title="影像科主任",
                    client_company="某三甲醫院",
                    content="這套AI診斷系統真的改變了我們的工作方式。診斷速度和準確性都有了質的飛躍，醫生們都很滿意。",
                    rating=Decimal("4.95"),
                    is_featured=True
                ),
                CaseTestimonial(
                    case_id=2,  # 智能客服機器人
                    client_name="李經理",
                    client_title="客服部經理",
                    client_company="某大型電商平台",
                    content="智能客服系統上線後，客戶滿意度明顯提升，同時大大減輕了人工客服的工作壓力。ROI超出預期！",
                    rating=Decimal("4.88"),
                    is_featured=True
                ),
                CaseTestimonial(
                    case_id=3,  # 企業知識管理平台
                    client_name="王總監",
                    client_title="IT總監",
                    client_company="某大型製造企業",
                    content="知識管理平台讓我們的企業知識真正活了起來，員工能快速找到需要的信息，工作效率大幅提升。",
                    rating=Decimal("4.92"),
                    is_featured=True
                )
            ]

            for testimonial in testimonials:
                db.add(testimonial)

            await db.commit()
            logger.info(f"已創建 {len(sample_cases)} 個示例 AI 案例和 {len(testimonials)} 個客戶評價")
