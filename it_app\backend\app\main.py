"""
FastAPI 主應用程序
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import time
import logging

from app.core.config import get_settings
from app.core.database import init_db, close_db
from app.api import api_router

settings = get_settings()

# 配置日誌
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """應用程序生命週期管理"""
    # 啟動時執行
    logger.info("正在啟動 AI速應後端 API...")
    
    # 初始化數據庫
    await init_db()
    logger.info("數據庫初始化完成")
    
    # 初始化示例數據
    await init_sample_data()
    logger.info("示例數據初始化完成")
    
    yield
    
    # 關閉時執行
    logger.info("正在關閉 AI速應後端 API...")
    await close_db()
    logger.info("數據庫連接已關閉")


# 創建 FastAPI 應用實例
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="AI接案平台後端API系統",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    lifespan=lifespan
)

# 添加 CORS 中間件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加受信任主機中間件（生產環境）
if not settings.debug:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["localhost", "127.0.0.1", "*.yourdomain.com"]
    )


# 請求處理時間中間件
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """添加請求處理時間頭部"""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


# 全局異常處理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP 異常處理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "data": None
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用異常處理器"""
    logger.error(f"未處理的異常: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "success": False,
            "message": "服務器內部錯誤" if not settings.debug else str(exc),
            "data": None
        }
    )


# 註冊 API 路由
app.include_router(api_router)


# 健康檢查端點
@app.get("/health")
async def health_check():
    """健康檢查"""
    return {
        "status": "healthy",
        "app_name": settings.app_name,
        "version": settings.app_version,
        "environment": settings.environment
    }


# 根路徑
@app.get("/")
async def root():
    """根路徑"""
    return {
        "message": f"歡迎使用 {settings.app_name}",
        "version": settings.app_version,
        "docs": "/docs" if settings.debug else "文檔已在生產環境中禁用"
    }


async def init_sample_data():
    """初始化示例數據"""
    from app.core.database import AsyncSessionLocal
    from app.models.service import AIService
    from sqlalchemy import select
    
    async with AsyncSessionLocal() as db:
        # 檢查是否已有數據
        stmt = select(AIService)
        result = await db.execute(stmt)
        existing_services = result.scalars().all()
        
        if existing_services:
            return  # 已有數據，跳過初始化
        
        # 創建示例 AI 服務
        sample_services = [
            AIService(
                name="RAG 檢索增強生成",
                description="智能文檔問答系統，支持企業知識庫檢索和智能問答",
                category="ai-search",
                price_min=5000,
                price_max=15000
            ),
            AIService(
                name="Dify 智能平台",
                description="低代碼AI應用開發平台，快速構建智能應用",
                category="ai-platform",
                price_min=8000,
                price_max=25000
            ),
            AIService(
                name="AI 視頻處理",
                description="智能視頻分析與生成，支持自動剪輯和內容識別",
                category="ai-media",
                price_min=10000,
                price_max=30000
            ),
            AIService(
                name="智能客服機器人",
                description="24/7 自動客戶服務，支持多語言和情感分析",
                category="ai-chat",
                price_min=3000,
                price_max=12000
            ),
            AIService(
                name="圖像識別系統",
                description="物體檢測與分類，支持實時圖像分析",
                category="ai-vision",
                price_min=6000,
                price_max=20000
            ),
            AIService(
                name="數據分析平台",
                description="智能數據洞察，自動生成分析報告",
                category="ai-analytics",
                price_min=7000,
                price_max=22000
            )
        ]
        
        for service in sample_services:
            db.add(service)
        
        await db.commit()
        logger.info(f"已創建 {len(sample_services)} 個示例 AI 服務")
