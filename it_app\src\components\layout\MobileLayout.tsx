import React, { ReactNode, useRef, Suspense } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Home,
  Briefcase,
  MessageCircle,
  User,
  Clock,
  Award,
  LogIn
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import clsx from 'clsx';
import Scene from '@/components/canvas/Scene';

interface MobileLayoutProps {
  children: ReactNode;
  showNavBar?: boolean;
  enable3D?: boolean;
}

const MobileLayout: React.FC<MobileLayoutProps> = ({
  children,
  showNavBar = true,
  enable3D = false
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const ref = useRef<HTMLDivElement>(null);
  const { state } = useAuth();

  // 導航項目配置
  const getNavItems = () => {
    const baseItems = [
      {
        path: '/',
        icon: Home,
        label: '首頁',
        activeColor: 'text-primary-600'
      },
      {
        path: '/services',
        icon: Briefcase,
        label: '服務',
        activeColor: 'text-primary-600'
      },
      {
        path: '/cases',
        icon: Award,
        label: '案例',
        activeColor: 'text-primary-600'
      }
    ];

    if (state.isAuthenticated) {
      // 已登入用戶顯示完整導航
      return [
        ...baseItems,
        {
          path: '/orders',
          icon: Clock,
          label: '訂單',
          activeColor: 'text-primary-600'
        },
        {
          path: '/profile',
          icon: User,
          label: '我的',
          activeColor: 'text-primary-600'
        }
      ];
    } else {
      // 未登入用戶顯示登入按鈕
      return [
        ...baseItems,
        {
          path: '/orders',
          icon: Clock,
          label: '訂單',
          activeColor: 'text-primary-600'
        },
        {
          path: '/login',
          icon: LogIn,
          label: '登入',
          activeColor: 'text-primary-600'
        }
      ];
    }
  };

  const navItems = getNavItems();

  // 獲取當前時間
  const getCurrentTime = () => {
    const now = new Date();
    return now.toLocaleTimeString('zh-TW', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  };

  return (
    <div className="min-h-screen bg-gray-100 flex justify-center items-center p-4">
      {/* 手機框架 */}
      <div
        ref={ref}
        className="w-full max-w-mobile h-mobile bg-white rounded-[40px] overflow-hidden shadow-2xl relative"
        style={{
          position: 'relative',
          width: '100%',
          height: '100%',
          overflow: 'auto',
          touchAction: 'auto',
        }}
      >
        
        {/* 手機狀態欄 */}
        <div className="h-11 bg-white flex justify-between items-center px-5 relative z-10">
          {/* 左側：時間 */}
          <div className="flex items-center">
            <span className="text-sm font-semibold text-black">
              {getCurrentTime()}
            </span>
          </div>
          
          {/* 右側：信號、WiFi、電池 */}
          <div className="flex items-center space-x-1">
            {/* 信號強度 */}
            <div className="flex items-center space-x-0.5">
              <div className="w-1 h-1 bg-black rounded-full"></div>
              <div className="w-1 h-2 bg-black rounded-full"></div>
              <div className="w-1 h-3 bg-black rounded-full"></div>
              <div className="w-1 h-4 bg-black rounded-full"></div>
            </div>
            
            {/* WiFi圖標 */}
            <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
              <path d="M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.07 2.93 1 9zm8 8l3 3 3-3c-1.65-1.65-4.34-1.65-6 0zm-4-4l2 2c2.76-2.76 7.24-2.76 10 0l2-2C15.14 9.14 8.87 9.14 5 13z"/>
            </svg>
            
            {/* 電池圖標 */}
            <div className="relative">
              <div className="w-6 h-3 border border-black rounded-sm flex items-center">
                <div className="w-4/5 h-2 bg-black rounded-sm mx-0.5"></div>
              </div>
              <div className="absolute -right-1 top-0.5 w-1 h-1.5 bg-black rounded-r-sm"></div>
            </div>
          </div>
        </div>

        {/* 主要內容區域 */}
        <div className={clsx(
          "flex-1 overflow-hidden",
          showNavBar ? "h-[calc(100%-44px-70px)]" : "h-[calc(100%-44px)]"
        )}>
          <div className="h-full overflow-y-auto">
            {children}
          </div>
        </div>

        {/* 底部導航欄 */}
        {showNavBar && (
          <div className="h-[70px] bg-white/90 backdrop-blur-lg border-t border-gray-200/50 flex justify-around items-center absolute bottom-0 w-full">
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.path;
              
              return (
                <button
                  key={item.path}
                  onClick={() => navigate(item.path)}
                  className={clsx(
                    "flex flex-col items-center py-2 px-4 transition-colors duration-200",
                    "active:scale-95 transform"
                  )}
                >
                  <Icon 
                    size={24} 
                    className={clsx(
                      "mb-1 transition-colors duration-200",
                      isActive ? item.activeColor : "text-gray-400"
                    )}
                  />
                  <span 
                    className={clsx(
                      "text-xs transition-colors duration-200",
                      isActive ? item.activeColor : "text-gray-400"
                    )}
                  >
                    {item.label}
                  </span>
                </button>
              );
            })}
          </div>
        )}

        {/* 3D Scene 背景 */}
        {enable3D && (
          <Suspense fallback={null}>
            <Scene
              style={{
                position: 'fixed',
                top: 0,
                left: 0,
                width: '100vw',
                height: '100vh',
                pointerEvents: 'none',
                zIndex: -1,
              }}
              eventSource={ref}
              eventPrefix='client'
            />
          </Suspense>
        )}
      </div>
    </div>
  );
};

export default MobileLayout; 