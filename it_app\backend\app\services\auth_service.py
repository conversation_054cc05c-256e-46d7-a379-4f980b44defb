"""
認證業務邏輯服務
"""

from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from fastapi import HTTPException, status

from app.core.security import (
    create_access_token, 
    create_refresh_token,
    get_password_hash, 
    verify_password,
    verify_token
)
from app.models.user import User, LoginLog
from app.schemas.auth import UserRegister, UserLogin
from app.core.config import get_settings

settings = get_settings()


class AuthService:
    """認證服務類"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def register_user(self, user_data: UserRegister) -> User:
        """
        註冊新用戶
        
        Args:
            user_data: 用戶註冊數據
            
        Returns:
            User: 創建的用戶對象
            
        Raises:
            HTTPException: 用戶名或郵箱已存在
        """
        # 檢查用戶名是否已存在
        stmt = select(User).where(User.username == user_data.username)
        result = await self.db.execute(stmt)
        if result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用戶名已存在"
            )
        
        # 檢查郵箱是否已存在
        stmt = select(User).where(User.email == user_data.email)
        result = await self.db.execute(stmt)
        if result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="郵箱已存在"
            )
        
        # 創建新用戶
        hashed_password = get_password_hash(user_data.password)
        new_user = User(
            username=user_data.username,
            email=user_data.email,
            password_hash=hashed_password,
            nickname=user_data.nickname or user_data.username,
            login_provider="email"
        )
        
        self.db.add(new_user)
        await self.db.commit()
        await self.db.refresh(new_user)
        
        return new_user
    
    async def authenticate_user(
        self, 
        username: str, 
        password: str
    ) -> Optional[User]:
        """
        驗證用戶憑據
        
        Args:
            username: 用戶名或郵箱
            password: 密碼
            
        Returns:
            User: 驗證成功的用戶對象，失敗返回 None
        """
        # 查找用戶（支持用戶名或郵箱登入）
        stmt = select(User).where(
            (User.username == username) | (User.email == username)
        )
        result = await self.db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            return None
        
        if not user.is_active:
            return None
        
        if not verify_password(password, user.password_hash):
            return None
        
        return user
    
    async def create_tokens(self, user: User) -> Dict[str, any]:
        """
        創建訪問令牌和刷新令牌

        Args:
            user: 用戶對象

        Returns:
            Dict: 包含令牌信息的字典
        """
        access_token = create_access_token(subject=user.id)
        refresh_token = create_refresh_token(subject=user.id)

        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": settings.access_token_expire_minutes * 60
        }
    
    async def get_user_by_token(self, token: str) -> Optional[User]:
        """
        通過令牌獲取用戶
        
        Args:
            token: JWT 令牌
            
        Returns:
            User: 用戶對象或 None
        """
        payload = verify_token(token)
        if not payload:
            return None
        
        user_id = payload.get("sub")
        if not user_id:
            return None
        
        stmt = select(User).where(User.id == int(user_id))
        result = await self.db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user or not user.is_active:
            return None
        
        return user
    
    async def log_login(
        self, 
        user: User, 
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        login_provider: str = "email",
        success: bool = True
    ) -> LoginLog:
        """
        記錄登入日誌
        
        Args:
            user: 用戶對象
            ip_address: IP 地址
            user_agent: 用戶代理
            login_provider: 登入方式
            success: 是否成功
            
        Returns:
            LoginLog: 登入記錄對象
        """
        login_log = LoginLog(
            user_id=user.id,
            ip_address=ip_address,
            user_agent=user_agent,
            login_provider=login_provider,
            success=success
        )
        
        self.db.add(login_log)
        await self.db.commit()
        await self.db.refresh(login_log)
        
        return login_log
