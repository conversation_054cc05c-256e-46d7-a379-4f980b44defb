'use client'

import { Canvas } from '@react-three/fiber'
import { Preload, PerformanceMonitor } from '@react-three/drei'
import { r3f } from '@/helpers/global'
import * as THREE from 'three'
import { useState } from 'react'

interface SceneProps {
  [key: string]: any
}

export default function Scene({ ...props }: SceneProps) {
  const [dpr, setDpr] = useState(1.5)

  return (
    <Canvas 
      {...props}
      dpr={dpr}
      performance={{ min: 0.5 }}
      frameloop="demand"
      onCreated={(state) => {
        state.gl.toneMapping = THREE.AgXToneMapping
        // 移動端優化設置
        state.gl.powerPreference = "high-performance"
        state.gl.antialias = false
      }}
    >
      {/* 性能監控 - 自動調節渲染質量 */}
      <PerformanceMonitor 
        onIncline={() => setDpr(2)} 
        onDecline={() => setDpr(1)}
        onFallback={() => setDpr(0.8)}
        flipflops={3}
      />
      
      {/* @ts-ignore */}
      <r3f.Out />
      <Preload all />
    </Canvas>
  )
}
