"""
Pydantic 數據驗證模型包
"""

from .common import ResponseModel, PaginationParams, PaginatedResponse
from .auth import (
    UserRegister, 
    UserLogin, 
    EmailCodeSend, 
    EmailCodeVerify,
    TokenResponse,
    UserResponse
)
from .user import UserUpdate, PasswordUpdate, UserProfile
from .case import (
    AICaseListResponse,
    AICaseDetailResponse,
    CaseTestimonialResponse,
    CaseStatsResponse,
    CaseCategoryResponse,
    CaseTechnologyResponse
)

__all__ = [
    # 通用
    "ResponseModel",
    "PaginationParams",
    "PaginatedResponse",

    # 認證
    "UserRegister",
    "UserLogin",
    "EmailCodeSend",
    "EmailCodeVerify",
    "TokenResponse",
    "UserResponse",

    # 用戶
    "UserUpdate",
    "PasswordUpdate",
    "UserProfile",

    # 案例
    "AICaseListResponse",
    "AICaseDetailResponse",
    "CaseTestimonialResponse",
    "CaseStatsResponse",
    "CaseCategoryResponse",
    "CaseTechnologyResponse",
]
