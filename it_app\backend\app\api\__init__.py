"""
API 路由包
"""

from fastapi import APIRouter
from .auth import router as auth_router
from .users import router as users_router
from .services import router as services_router
from .cases import router as cases_router

# 創建主 API 路由器
api_router = APIRouter(prefix="/api")

# 註冊子路由
api_router.include_router(auth_router, prefix="/auth", tags=["認證"])
api_router.include_router(users_router, prefix="/users", tags=["用戶管理"])
api_router.include_router(services_router, prefix="/services", tags=["AI服務"])
api_router.include_router(cases_router, prefix="/cases", tags=["AI案例"])

__all__ = ["api_router"]
