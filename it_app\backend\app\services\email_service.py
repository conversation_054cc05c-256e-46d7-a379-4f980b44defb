"""
郵件服務
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Dict, Optional
import asyncio
from datetime import datetime, timedelta

from app.core.config import get_settings
from app.core.security import generate_verification_code

settings = get_settings()


class EmailService:
    """郵件服務類"""
    
    def __init__(self):
        self.smtp_host = settings.smtp_host
        self.smtp_port = settings.smtp_port
        self.smtp_username = settings.smtp_username
        self.smtp_password = settings.smtp_password
        self.email_from = settings.email_from
        
        # 內存中的驗證碼存儲（生產環境應使用 Redis）
        self._verification_codes: Dict[str, Dict] = {}
    
    async def send_verification_code(self, email: str) -> str:
        """
        發送驗證碼到指定郵箱
        
        Args:
            email: 目標郵箱
            
        Returns:
            str: 驗證碼
        """
        # 生成驗證碼
        code = generate_verification_code()
        
        # 存儲驗證碼（5分鐘有效期）
        self._verification_codes[email] = {
            "code": code,
            "expires_at": datetime.utcnow() + timedelta(minutes=5),
            "attempts": 0
        }
        
        # 發送郵件
        await self._send_email(
            to_email=email,
            subject="AI速應 - 登入驗證碼",
            body=self._get_verification_email_body(code)
        )
        
        return code
    
    def verify_code(self, email: str, code: str) -> bool:
        """
        驗證郵箱驗證碼
        
        Args:
            email: 郵箱地址
            code: 驗證碼
            
        Returns:
            bool: 驗證是否成功
        """
        if email not in self._verification_codes:
            return False
        
        stored_data = self._verification_codes[email]
        
        # 檢查是否過期
        if datetime.utcnow() > stored_data["expires_at"]:
            del self._verification_codes[email]
            return False
        
        # 檢查嘗試次數（最多3次）
        if stored_data["attempts"] >= 3:
            del self._verification_codes[email]
            return False
        
        # 驗證碼匹配
        if stored_data["code"] == code:
            del self._verification_codes[email]
            return True
        
        # 增加嘗試次數
        stored_data["attempts"] += 1
        return False
    
    async def _send_email(
        self, 
        to_email: str, 
        subject: str, 
        body: str,
        is_html: bool = True
    ) -> None:
        """
        發送郵件
        
        Args:
            to_email: 收件人郵箱
            subject: 郵件主題
            body: 郵件內容
            is_html: 是否為 HTML 格式
        """
        if not all([self.smtp_username, self.smtp_password, self.email_from]):
            # 開發環境下模擬發送
            print(f"模擬發送郵件到 {to_email}")
            print(f"主題: {subject}")
            print(f"內容: {body}")
            return
        
        # 創建郵件
        msg = MIMEMultipart()
        msg['From'] = self.email_from
        msg['To'] = to_email
        msg['Subject'] = subject
        
        # 添加郵件內容
        msg.attach(MIMEText(body, 'html' if is_html else 'plain', 'utf-8'))
        
        # 發送郵件
        def send_sync():
            try:
                with smtplib.SMTP(self.smtp_host, self.smtp_port) as server:
                    server.starttls()
                    server.login(self.smtp_username, self.smtp_password)
                    server.send_message(msg)
            except Exception as e:
                print(f"發送郵件失敗: {e}")
        
        # 在線程池中執行同步操作
        await asyncio.get_event_loop().run_in_executor(None, send_sync)
    
    def _get_verification_email_body(self, code: str) -> str:
        """
        獲取驗證碼郵件內容
        
        Args:
            code: 驗證碼
            
        Returns:
            str: HTML 格式的郵件內容
        """
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>AI速應 - 登入驗證碼</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #3b82f6;">AI速應</h1>
                    <p style="color: #666;">專業的AI接案服務平台</p>
                </div>
                
                <div style="background: #f8fafc; padding: 30px; border-radius: 10px; text-align: center;">
                    <h2 style="color: #1f2937; margin-bottom: 20px;">登入驗證碼</h2>
                    <p style="font-size: 16px; margin-bottom: 30px;">
                        您的登入驗證碼是：
                    </p>
                    <div style="background: #3b82f6; color: white; font-size: 32px; font-weight: bold; 
                                padding: 20px; border-radius: 8px; letter-spacing: 8px; margin: 20px 0;">
                        {code}
                    </div>
                    <p style="color: #666; font-size: 14px;">
                        驗證碼有效期為 5 分鐘，請及時使用。<br>
                        如果這不是您的操作，請忽略此郵件。
                    </p>
                </div>
                
                <div style="text-align: center; margin-top: 30px; color: #666; font-size: 12px;">
                    <p>此郵件由系統自動發送，請勿回復。</p>
                    <p>&copy; 2024 AI速應. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """
