"""
認證 API 測試
"""

import pytest
from httpx import AsyncClient
from fastapi.testclient import TestClient

from app.main import app

client = TestClient(app)


def test_register_user():
    """測試用戶註冊"""
    response = client.post("/api/auth/register", json={
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "TestPass123",
        "nickname": "測試用戶"
    })
    
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "user_id" in data["data"]


def test_login_user():
    """測試用戶登入"""
    # 先註冊用戶
    client.post("/api/auth/register", json={
        "username": "logintest",
        "email": "<EMAIL>", 
        "password": "TestPass123"
    })
    
    # 測試登入
    response = client.post("/api/auth/login", json={
        "username": "logintest",
        "password": "TestPass123"
    })
    
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "access_token" in data["data"]
    assert "refresh_token" in data["data"]


def test_get_current_user():
    """測試獲取當前用戶信息"""
    # 註冊並登入用戶
    client.post("/api/auth/register", json={
        "username": "currentuser",
        "email": "<EMAIL>",
        "password": "TestPass123"
    })
    
    login_response = client.post("/api/auth/login", json={
        "username": "currentuser",
        "password": "TestPass123"
    })
    
    token = login_response.json()["data"]["access_token"]
    
    # 測試獲取用戶信息
    response = client.get("/api/auth/me", headers={
        "Authorization": f"Bearer {token}"
    })
    
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["data"]["username"] == "currentuser"


def test_invalid_login():
    """測試無效登入"""
    response = client.post("/api/auth/login", json={
        "username": "nonexistent",
        "password": "wrongpassword"
    })
    
    assert response.status_code == 401


def test_unauthorized_access():
    """測試未授權訪問"""
    response = client.get("/api/auth/me")
    assert response.status_code == 401
