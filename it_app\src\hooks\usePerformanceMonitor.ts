import { useEffect, useRef, useState } from 'react';
import { performanceManager, Performance3DSettings, DeviceCapability } from '@/utils/performanceManager';

interface PerformanceMetrics {
  fps: number;
  memoryUsage: number;
  loadTime: number;
  isOptimized: boolean;
}

interface UsePerformanceMonitorReturn {
  metrics: PerformanceMetrics;
  settings: Performance3DSettings | null;
  deviceCapability: DeviceCapability | null;
  isLowPerformance: boolean;
  adjustSettings: () => void;
}

/**
 * 性能監控 Hook
 * 自動檢測設備性能並調整 3D 效果設置
 */
export const usePerformanceMonitor = (): UsePerformanceMonitorReturn => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 0,
    memoryUsage: 0,
    loadTime: 0,
    isOptimized: false
  });
  
  const [settings, setSettings] = useState<Performance3DSettings | null>(null);
  const [deviceCapability, setDeviceCapability] = useState<DeviceCapability | null>(null);
  const [isLowPerformance, setIsLowPerformance] = useState(false);
  
  const frameCountRef = useRef(0);
  const lastTimeRef = useRef(performance.now());
  const fpsIntervalRef = useRef<NodeJS.Timeout>();
  const memoryIntervalRef = useRef<NodeJS.Timeout>();

  // 初始化性能檢測
  useEffect(() => {
    // 檢測設備能力
    const capability = performanceManager.detectDeviceCapability();
    setDeviceCapability(capability);

    // 獲取優化設置
    const optimalSettings = performanceManager.getOptimal3DSettings();
    setSettings(optimalSettings);

    // 記錄載入時間
    performanceManager.recordLoadTime();

    // 判斷是否為低性能設備
    setIsLowPerformance(capability.isMobile || !capability.isHighEnd);

    console.log('Performance Manager initialized:', {
      capability,
      settings: optimalSettings
    });
  }, []);

  // FPS 監控
  useEffect(() => {
    const measureFPS = () => {
      frameCountRef.current++;
      
      const now = performance.now();
      const delta = now - lastTimeRef.current;
      
      // 每秒計算一次 FPS
      if (delta >= 1000) {
        const fps = Math.round((frameCountRef.current * 1000) / delta);
        
        // 記錄 FPS
        performanceManager.recordFPS(fps);
        
        // 更新狀態
        setMetrics(prev => ({
          ...prev,
          fps,
          isOptimized: fps >= 30
        }));
        
        // 重置計數器
        frameCountRef.current = 0;
        lastTimeRef.current = now;
      }
      
      // 繼續下一幀
      requestAnimationFrame(measureFPS);
    };

    // 開始 FPS 監控
    const animationId = requestAnimationFrame(measureFPS);

    return () => {
      cancelAnimationFrame(animationId);
    };
  }, []);

  // 內存監控
  useEffect(() => {
    const monitorMemory = () => {
      performanceManager.recordMemoryUsage();
      
      if ('memory' in performance && (performance as any).memory) {
        const memory = (performance as any).memory;
        const usedMB = memory.usedJSHeapSize / (1024 * 1024);
        
        setMetrics(prev => ({
          ...prev,
          memoryUsage: usedMB
        }));
      }
    };

    // 每 5 秒監控一次內存
    memoryIntervalRef.current = setInterval(monitorMemory, 5000);
    
    // 立即執行一次
    monitorMemory();

    return () => {
      if (memoryIntervalRef.current) {
        clearInterval(memoryIntervalRef.current);
      }
    };
  }, []);

  // 載入時間監控
  useEffect(() => {
    const updateLoadTime = () => {
      const report = performanceManager.getPerformanceReport();
      setMetrics(prev => ({
        ...prev,
        loadTime: report.metrics.loadTime
      }));
    };

    // 頁面載入完成後更新載入時間
    if (document.readyState === 'complete') {
      updateLoadTime();
    } else {
      window.addEventListener('load', updateLoadTime);
      return () => window.removeEventListener('load', updateLoadTime);
    }
  }, []);

  // 手動調整設置
  const adjustSettings = () => {
    performanceManager.adjustPerformanceBasedOnMetrics();
    const newSettings = performanceManager.getOptimal3DSettings();
    setSettings(newSettings);
    
    console.log('Performance settings adjusted:', newSettings);
  };

  // 自動調整監控
  useEffect(() => {
    const autoAdjustInterval = setInterval(() => {
      const avgFPS = performanceManager.getAverageFPS();
      
      // 如果 FPS 持續過低，自動調整
      if (avgFPS > 0 && avgFPS < 25) {
        adjustSettings();
      }
    }, 30000); // 每 30 秒檢查一次

    return () => clearInterval(autoAdjustInterval);
  }, []);

  return {
    metrics,
    settings,
    deviceCapability,
    isLowPerformance,
    adjustSettings
  };
};

/**
 * 性能警告 Hook
 * 當性能過低時顯示警告
 */
export const usePerformanceWarning = () => {
  const { metrics, isLowPerformance } = usePerformanceMonitor();
  const [showWarning, setShowWarning] = useState(false);
  const [warningMessage, setWarningMessage] = useState('');

  useEffect(() => {
    let shouldWarn = false;
    let message = '';

    if (metrics.fps > 0 && metrics.fps < 20) {
      shouldWarn = true;
      message = '檢測到性能問題，建議關閉部分 3D 效果以提升體驗';
    } else if (metrics.memoryUsage > 150) {
      shouldWarn = true;
      message = '內存使用過高，建議刷新頁面或關閉其他標籤頁';
    } else if (metrics.loadTime > 5000) {
      shouldWarn = true;
      message = '頁面載入較慢，建議檢查網絡連接';
    }

    setShowWarning(shouldWarn);
    setWarningMessage(message);
  }, [metrics]);

  return {
    showWarning,
    warningMessage,
    isLowPerformance,
    dismissWarning: () => setShowWarning(false)
  };
};
