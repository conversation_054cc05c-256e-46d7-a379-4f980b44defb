import { useState, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';

/**
 * 會員功能 Hook
 * 用於處理需要會員權限的功能
 */
export const useMembership = () => {
  const { state } = useAuth();
  const [showPrompt, setShowPrompt] = useState(false);
  const [promptFeature, setPromptFeature] = useState('');
  const [promptDescription, setPromptDescription] = useState('');

  /**
   * 檢查是否為會員
   */
  const isMember = state.isAuthenticated;

  /**
   * 要求會員權限
   * @param feature 功能名稱
   * @param description 功能描述
   * @param callback 如果是會員則執行的回調函數
   */
  const requireMembership = useCallback((
    feature: string,
    description?: string,
    callback?: () => void
  ) => {
    if (isMember) {
      // 如果是會員，直接執行回調
      callback?.();
      return true;
    } else {
      // 如果不是會員，顯示會員提示
      setPromptFeature(feature);
      setPromptDescription(description || '');
      setShowPrompt(true);
      return false;
    }
  }, [isMember]);

  /**
   * 關閉會員提示
   */
  const closePrompt = useCallback(() => {
    setShowPrompt(false);
    setPromptFeature('');
    setPromptDescription('');
  }, []);

  /**
   * 會員功能包裝器
   * 用於包裝需要會員權限的組件或功能
   */
  const withMembership = useCallback((
    feature: string,
    description?: string
  ) => {
    return (callback: () => void) => {
      requireMembership(feature, description, callback);
    };
  }, [requireMembership]);

  return {
    isMember,
    showPrompt,
    promptFeature,
    promptDescription,
    requireMembership,
    closePrompt,
    withMembership
  };
};
