/**
 * 性能測試工具
 * 用於測試和驗證性能優化效果
 */

export interface PerformanceTestResult {
  testName: string;
  duration: number;
  fps: number;
  memoryUsage: number;
  passed: boolean;
  details: string;
}

export class PerformanceTest {
  private results: PerformanceTestResult[] = [];

  /**
   * 測試頁面載入性能
   */
  async testPageLoad(): Promise<PerformanceTestResult> {
    const startTime = performance.now();
    
    return new Promise((resolve) => {
      const checkLoad = () => {
        if (document.readyState === 'complete') {
          const duration = performance.now() - startTime;
          const passed = duration < 3000; // 3秒內載入完成
          
          resolve({
            testName: '頁面載入測試',
            duration,
            fps: 0,
            memoryUsage: this.getMemoryUsage(),
            passed,
            details: `載入時間: ${duration.toFixed(2)}ms, 目標: <3000ms`
          });
        } else {
          setTimeout(checkLoad, 100);
        }
      };
      
      checkLoad();
    });
  }

  /**
   * 測試 3D 渲染性能
   */
  async test3DPerformance(duration: number = 5000): Promise<PerformanceTestResult> {
    const fpsReadings: number[] = [];
    let frameCount = 0;
    let lastTime = performance.now();
    
    return new Promise((resolve) => {
      const measureFPS = () => {
        frameCount++;
        const now = performance.now();
        const delta = now - lastTime;
        
        if (delta >= 1000) {
          const fps = Math.round((frameCount * 1000) / delta);
          fpsReadings.push(fps);
          frameCount = 0;
          lastTime = now;
        }
        
        if (now - lastTime < duration) {
          requestAnimationFrame(measureFPS);
        } else {
          const avgFPS = fpsReadings.reduce((a, b) => a + b, 0) / fpsReadings.length;
          const passed = avgFPS >= 30;
          
          resolve({
            testName: '3D 渲染性能測試',
            duration,
            fps: avgFPS,
            memoryUsage: this.getMemoryUsage(),
            passed,
            details: `平均 FPS: ${avgFPS.toFixed(1)}, 目標: ≥30fps`
          });
        }
      };
      
      requestAnimationFrame(measureFPS);
    });
  }

  /**
   * 測試內存使用
   */
  testMemoryUsage(): PerformanceTestResult {
    const memoryUsage = this.getMemoryUsage();
    const passed = memoryUsage < 100; // 100MB 以下
    
    return {
      testName: '內存使用測試',
      duration: 0,
      fps: 0,
      memoryUsage,
      passed,
      details: `內存使用: ${memoryUsage.toFixed(2)}MB, 目標: <100MB`
    };
  }

  /**
   * 測試響應式設計
   */
  testResponsiveDesign(): PerformanceTestResult {
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    // 檢查是否有橫向滾動條
    const hasHorizontalScroll = document.body.scrollWidth > viewportWidth;
    
    // 檢查關鍵元素是否可見
    const criticalElements = [
      'nav', 'main', 'header', '.navigation'
    ];
    
    let visibleElements = 0;
    criticalElements.forEach(selector => {
      const element = document.querySelector(selector);
      if (element && this.isElementVisible(element)) {
        visibleElements++;
      }
    });
    
    const passed = !hasHorizontalScroll && visibleElements > 0;
    
    return {
      testName: '響應式設計測試',
      duration: 0,
      fps: 0,
      memoryUsage: 0,
      passed,
      details: `視窗: ${viewportWidth}x${viewportHeight}, 橫向滾動: ${hasHorizontalScroll ? '是' : '否'}, 可見元素: ${visibleElements}`
    };
  }

  /**
   * 測試 SEO 基礎要素
   */
  testSEOBasics(): PerformanceTestResult {
    const checks = {
      title: !!document.title && document.title.length > 0,
      description: !!document.querySelector('meta[name="description"]'),
      keywords: !!document.querySelector('meta[name="keywords"]'),
      ogTitle: !!document.querySelector('meta[property="og:title"]'),
      ogDescription: !!document.querySelector('meta[property="og:description"]'),
      canonical: !!document.querySelector('link[rel="canonical"]')
    };
    
    const passedChecks = Object.values(checks).filter(Boolean).length;
    const totalChecks = Object.keys(checks).length;
    const passed = passedChecks >= totalChecks * 0.8; // 80% 通過率
    
    return {
      testName: 'SEO 基礎測試',
      duration: 0,
      fps: 0,
      memoryUsage: 0,
      passed,
      details: `通過檢查: ${passedChecks}/${totalChecks}, 檢查項目: ${Object.entries(checks).map(([key, value]) => `${key}: ${value ? '✓' : '✗'}`).join(', ')}`
    };
  }

  /**
   * 運行所有測試
   */
  async runAllTests(): Promise<PerformanceTestResult[]> {
    console.log('開始性能測試...');
    
    // 清空之前的結果
    this.results = [];
    
    try {
      // 頁面載入測試
      const loadTest = await this.testPageLoad();
      this.results.push(loadTest);
      
      // 3D 性能測試
      const renderTest = await this.test3DPerformance();
      this.results.push(renderTest);
      
      // 內存測試
      const memoryTest = this.testMemoryUsage();
      this.results.push(memoryTest);
      
      // 響應式測試
      const responsiveTest = this.testResponsiveDesign();
      this.results.push(responsiveTest);
      
      // SEO 測試
      const seoTest = this.testSEOBasics();
      this.results.push(seoTest);
      
    } catch (error) {
      console.error('性能測試出錯:', error);
    }
    
    return this.results;
  }

  /**
   * 生成測試報告
   */
  generateReport(): string {
    const passedTests = this.results.filter(r => r.passed).length;
    const totalTests = this.results.length;
    const passRate = (passedTests / totalTests * 100).toFixed(1);
    
    let report = `性能測試報告\n`;
    report += `=================\n`;
    report += `總測試數: ${totalTests}\n`;
    report += `通過測試: ${passedTests}\n`;
    report += `通過率: ${passRate}%\n\n`;
    
    this.results.forEach(result => {
      report += `${result.testName}: ${result.passed ? '✓ 通過' : '✗ 失敗'}\n`;
      report += `  ${result.details}\n\n`;
    });
    
    return report;
  }

  /**
   * 獲取內存使用量（MB）
   */
  private getMemoryUsage(): number {
    if ('memory' in performance && (performance as any).memory) {
      const memory = (performance as any).memory;
      return memory.usedJSHeapSize / (1024 * 1024);
    }
    return 0;
  }

  /**
   * 檢查元素是否可見
   */
  private isElementVisible(element: Element): boolean {
    const rect = element.getBoundingClientRect();
    return rect.width > 0 && rect.height > 0;
  }
}

// 導出單例實例
export const performanceTest = new PerformanceTest();
