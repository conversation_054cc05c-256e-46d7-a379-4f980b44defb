"""
AI 服務相關數據模型
"""

from decimal import Decimal
from typing import Optional
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, Numeric, String, Text
from sqlalchemy.orm import Mapped, mapped_column

from app.core.database import Base
from .base import TimestampMixin


class AIService(Base, TimestampMixin):
    """AI 服務模型"""
    
    __tablename__ = "ai_services"
    
    # 主鍵
    id: Mapped[int] = mapped_column(
        Integer, 
        primary_key=True, 
        autoincrement=True,
        comment="服務ID"
    )
    
    # 基本信息
    name: Mapped[str] = mapped_column(
        String(100), 
        nullable=False,
        comment="服務名稱"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text, 
        nullable=True,
        comment="服務描述"
    )
    
    category: Mapped[str] = mapped_column(
        String(50), 
        nullable=False,
        comment="服務分類"
    )
    
    # 價格信息
    price_min: Mapped[Decimal] = mapped_column(
        Numeric(10, 2), 
        nullable=False,
        comment="最低價格"
    )
    
    price_max: Mapped[Decimal] = mapped_column(
        Numeric(10, 2), 
        nullable=False,
        comment="最高價格"
    )
    
    # 狀態信息
    is_active: Mapped[bool] = mapped_column(
        Boolean, 
        default=True, 
        nullable=False,
        comment="是否啟用"
    )
    
    def __repr__(self) -> str:
        return f"<AIService(id={self.id}, name='{self.name}', category='{self.category}')>"
