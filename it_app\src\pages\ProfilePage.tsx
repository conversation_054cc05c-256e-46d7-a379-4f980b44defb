import React, { Suspense, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  User,
  Settings,
  Bell,
  Shield,
  HelpCircle,
  LogOut,
  ChevronRight,
  Star,
  Award,
  TrendingUp,
  Lock
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import MobileLayout from '@/components/layout/MobileLayout';
import { View, Common } from '@/components/canvas/View';
import { Progress3D } from '@/components/canvas/Progress3D';
import { ParticleSystem } from '@/components/canvas/ParticleSystem';

const ProfilePage: React.FC = () => {
  const navigate = useNavigate();
  const { state, logout } = useAuth();

  // 檢查是否已登入，如果未登入顯示登入提示
  useEffect(() => {
    if (!state.isAuthenticated) {
      // 不強制重定向，而是顯示登入提示頁面
    }
  }, [state.isAuthenticated]);

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  const menuItems = [
    {
      icon: User,
      label: '個人信息',
      description: '編輯個人資料',
      onClick: () => navigate('/profile/edit'),
      color: 'text-blue-600'
    },
    {
      icon: Bell,
      label: '通知設置',
      description: '管理推送通知',
      onClick: () => navigate('/settings/notifications'),
      color: 'text-green-600'
    },
    {
      icon: Shield,
      label: '隱私設置',
      description: '帳戶安全設置',
      onClick: () => navigate('/settings/privacy'),
      color: 'text-purple-600'
    },
    {
      icon: HelpCircle,
      label: '幫助中心',
      description: '常見問題解答',
      onClick: () => navigate('/help'),
      color: 'text-orange-600'
    }
  ];

  const stats = [
    { label: '完成項目', value: 12, icon: Award, color: '#10b981' },
    { label: '客戶評分', value: 4.8, icon: Star, color: '#f59e0b' },
    { label: '成長指數', value: 85, icon: TrendingUp, color: '#3b82f6' }
  ];

  // 如果未登入，顯示登入提示
  if (!state.isAuthenticated) {
    return (
      <MobileLayout enable3D={true}>
        <div className="flex flex-col h-full bg-gray-50 relative">
          {/* 3D 背景效果 */}
          <View className="absolute inset-0 opacity-10">
            <Suspense fallback={null}>
              <ParticleSystem
                count={100}
                color="#3b82f6"
                size={0.002}
                speed={0.03}
                spread={5}
              />
              <Common />
            </Suspense>
          </View>

          {/* 登入提示內容 */}
          <div className="flex-1 flex items-center justify-center px-6 relative z-10">
            <div className="text-center max-w-sm">
              <div className="w-20 h-20 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <Lock className="w-10 h-10 text-white" />
              </div>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                會員專屬功能
              </h2>

              <p className="text-gray-600 mb-8 leading-relaxed">
                登入您的帳號以訪問個人資料、查看訂單記錄、管理收藏等會員專屬功能
              </p>

              <div className="space-y-3">
                <button
                  onClick={() => navigate('/login')}
                  className="w-full bg-gradient-to-r from-primary-600 to-primary-700 text-white py-3 px-6 rounded-lg font-medium hover:from-primary-700 hover:to-primary-800 transition-all"
                >
                  立即登入
                </button>

                <button
                  onClick={() => navigate('/register')}
                  className="w-full border border-primary-600 text-primary-600 py-3 px-6 rounded-lg font-medium hover:bg-primary-50 transition-colors"
                >
                  註冊新帳號
                </button>
              </div>

              <div className="mt-8 pt-6 border-t border-gray-200">
                <p className="text-sm text-gray-500 mb-4">會員專享功能</p>
                <div className="grid grid-cols-2 gap-4 text-xs">
                  <div className="flex items-center space-x-2">
                    <Star className="w-4 h-4 text-yellow-500" />
                    <span>收藏案例</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Settings className="w-4 h-4 text-gray-500" />
                    <span>個人設置</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Bell className="w-4 h-4 text-blue-500" />
                    <span>消息通知</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Award className="w-4 h-4 text-purple-500" />
                    <span>專屬優惠</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </MobileLayout>
    );
  }

  return (
    <MobileLayout enable3D={true}>
      <div className="flex flex-col h-full bg-gray-50 relative">
        {/* 3D 背景效果 */}
        <View className="absolute inset-0 opacity-10">
          <Suspense fallback={null}>
            <ParticleSystem 
              count={200}
              color="#3b82f6"
              size={0.002}
              speed={0.03}
              spread={5}
            />
            <Common />
          </Suspense>
        </View>

        {/* 用戶信息卡片 */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 px-6 py-8 text-white relative z-10">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
              <User className="w-8 h-8" />
            </div>
            <div className="flex-1">
              <h2 className="text-xl font-bold">
                {state.user?.nickname || state.user?.username || '用戶'}
              </h2>
              <p className="text-primary-100 text-sm">
                AI 開發專家
              </p>
              <div className="flex items-center mt-2">
                <Star className="w-4 h-4 text-yellow-300 mr-1" />
                <span className="text-sm">4.8 評分</span>
              </div>
            </div>
            <button
              onClick={() => navigate('/profile/edit')}
              className="p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
            >
              <Settings className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* 統計數據 */}
        <div className="px-6 py-4 relative z-10">
          <div className="bg-white rounded-lg shadow-sm p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">我的統計</h3>
            <div className="grid grid-cols-3 gap-4">
              {stats.map((stat, index) => {
                const IconComponent = stat.icon;
                return (
                  <div key={index} className="text-center">
                    <div className="h-16 mb-2">
                      <View className="w-full h-full">
                        <Suspense fallback={null}>
                          <Progress3D
                            progress={typeof stat.value === 'number' && stat.value <= 100 ? stat.value : 80}
                            size={0.4}
                            color={stat.color}
                            position={[0, 0, 0]}
                            animated={true}
                          />
                          <Common />
                        </Suspense>
                      </View>
                    </div>
                    <div className="text-lg font-bold text-gray-900">
                      {stat.value}
                      {stat.label === '客戶評分' && '/5'}
                      {stat.label === '成長指數' && '%'}
                    </div>
                    <div className="text-xs text-gray-600">{stat.label}</div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* 菜單列表 */}
        <div className="flex-1 px-6 pb-6 relative z-10">
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            {menuItems.map((item, index) => {
              const IconComponent = item.icon;
              return (
                <button
                  key={index}
                  onClick={item.onClick}
                  className="w-full px-4 py-4 flex items-center space-x-3 hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0"
                >
                  <div className={`p-2 rounded-lg bg-gray-100 ${item.color}`}>
                    <IconComponent className="w-5 h-5" />
                  </div>
                  <div className="flex-1 text-left">
                    <div className="font-medium text-gray-900">{item.label}</div>
                    <div className="text-sm text-gray-500">{item.description}</div>
                  </div>
                  <ChevronRight className="w-5 h-5 text-gray-400" />
                </button>
              );
            })}
          </div>

          {/* 登出按鈕 */}
          <div className="mt-4">
            <button
              onClick={handleLogout}
              className="w-full bg-white rounded-lg shadow-sm px-4 py-4 flex items-center justify-center space-x-2 text-red-600 hover:bg-red-50 transition-colors"
            >
              <LogOut className="w-5 h-5" />
              <span className="font-medium">登出</span>
            </button>
          </div>
        </div>
      </div>
    </MobileLayout>
  );
};

export default ProfilePage;
