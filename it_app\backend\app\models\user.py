"""
用戶相關數據模型
包含用戶表和登入記錄表
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import <PERSON>olean, DateTime, ForeignKey, Integer, String, Text, func
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import Base
from .base import TimestampMixin


class User(Base, TimestampMixin):
    """用戶模型"""
    
    __tablename__ = "users"
    
    # 主鍵
    id: Mapped[int] = mapped_column(
        Integer, 
        primary_key=True, 
        autoincrement=True,
        comment="用戶ID"
    )
    
    # 基本信息
    username: Mapped[str] = mapped_column(
        String(50), 
        unique=True, 
        nullable=False,
        comment="用戶名"
    )
    
    email: Mapped[str] = mapped_column(
        String(100), 
        unique=True, 
        nullable=False,
        comment="電子郵件"
    )
    
    password_hash: Mapped[str] = mapped_column(
        String(255), 
        nullable=False,
        comment="加密密碼"
    )
    
    nickname: Mapped[Optional[str]] = mapped_column(
        String(100), 
        nullable=True,
        comment="暱稱"
    )
    
    avatar_url: Mapped[Optional[str]] = mapped_column(
        String(255), 
        nullable=True,
        comment="頭像URL"
    )
    
    # 狀態信息
    is_active: Mapped[bool] = mapped_column(
        Boolean, 
        default=True, 
        nullable=False,
        comment="帳號狀態"
    )
    
    # 登入方式
    login_provider: Mapped[str] = mapped_column(
        String(20), 
        default="email", 
        nullable=False,
        comment="登入方式"
    )
    
    provider_id: Mapped[Optional[str]] = mapped_column(
        String(100), 
        nullable=True,
        comment="第三方ID"
    )
    
    # 關聯關係
    login_logs: Mapped[list["LoginLog"]] = relationship(
        "LoginLog", 
        back_populates="user",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"


class LoginLog(Base):
    """登入記錄模型"""
    
    __tablename__ = "login_logs"
    
    # 主鍵
    id: Mapped[int] = mapped_column(
        Integer, 
        primary_key=True, 
        autoincrement=True,
        comment="記錄ID"
    )
    
    # 外鍵
    user_id: Mapped[int] = mapped_column(
        Integer, 
        ForeignKey("users.id", ondelete="CASCADE"), 
        nullable=False,
        comment="用戶ID"
    )
    
    # 登入信息
    login_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        comment="登入時間"
    )
    
    ip_address: Mapped[Optional[str]] = mapped_column(
        String(45), 
        nullable=True,
        comment="IP地址"
    )
    
    user_agent: Mapped[Optional[str]] = mapped_column(
        Text, 
        nullable=True,
        comment="瀏覽器信息"
    )
    
    login_provider: Mapped[str] = mapped_column(
        String(20), 
        nullable=False,
        comment="登入方式"
    )
    
    success: Mapped[bool] = mapped_column(
        Boolean, 
        default=True, 
        nullable=False,
        comment="是否成功"
    )
    
    # 關聯關係
    user: Mapped["User"] = relationship("User", back_populates="login_logs")
    
    def __repr__(self) -> str:
        return f"<LoginLog(id={self.id}, user_id={self.user_id}, login_time='{self.login_time}')>"
