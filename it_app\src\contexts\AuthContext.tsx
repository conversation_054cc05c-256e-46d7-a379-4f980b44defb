import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { User, LoginRequest, RegisterRequest } from '@/types';
import { authService } from '@/services/authService';

// 認證狀態類型定義
interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
}

// Action類型定義
type AuthAction = 
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: User }
  | { type: 'AUTH_FAILURE'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'CLEAR_ERROR' }
  | { type: 'UPDATE_USER'; payload: Partial<User> };

// 初始狀態
const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  loading: false,
  error: null,
};

// Reducer函數
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        loading: true,
        error: null,
      };
    case 'AUTH_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload,
        loading: false,
        error: null,
      };
    case 'AUTH_FAILURE':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        loading: false,
        error: action.payload,
      };
    case 'LOGOUT':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        loading: false,
        error: null,
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    case 'UPDATE_USER':
      return {
        ...state,
        user: state.user ? { ...state.user, ...action.payload } : null,
      };
    default:
      return state;
  }
};

// Context類型定義
interface AuthContextType {
  state: AuthState;
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  logout: () => void;
  thirdPartyLogin: (platform: string, code: string) => Promise<void>;
  clearError: () => void;
  updateUser: (userData: Partial<User>) => void;
  checkAuthStatus: () => Promise<void>;
}

// 創建Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider組件
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // 檢查認證狀態
  const checkAuthStatus = async () => {
    const isAuthenticated = authService.isAuthenticated();
    const userId = authService.getCurrentUserId();

    if (isAuthenticated && userId) {
      try {
        dispatch({ type: 'AUTH_START' });
        const user = await authService.getUserProfile(userId);
        dispatch({ type: 'AUTH_SUCCESS', payload: user });
      } catch (error) {
        console.error('檢查認證狀態失敗:', error);
        // 如果獲取用戶信息失敗，清除本地存儲
        authService.logout();
        dispatch({ type: 'AUTH_FAILURE', payload: '認證已過期，請重新登錄' });
      }
    }
  };

  // 組件掛載時檢查認證狀態
  useEffect(() => {
    checkAuthStatus();
  }, []);

  // 登錄函數
  const login = async (credentials: LoginRequest) => {
    try {
      dispatch({ type: 'AUTH_START' });
      const response = await authService.login(credentials);
      
      if (response.code === 200 && response.data) {
        const user = await authService.getUserProfile(response.data.userId);
        dispatch({ type: 'AUTH_SUCCESS', payload: user });
      } else {
        dispatch({ type: 'AUTH_FAILURE', payload: response.message || '登錄失敗' });
      }
    } catch (error: any) {
      dispatch({ type: 'AUTH_FAILURE', payload: error.message || '網絡錯誤，請稍後重試' });
    }
  };

  // 註冊函數
  const register = async (userData: RegisterRequest) => {
    try {
      dispatch({ type: 'AUTH_START' });
      const response = await authService.register(userData);
      
      if (response.code === 200 && response.data) {
        const user = await authService.getUserProfile(response.data.userId);
        dispatch({ type: 'AUTH_SUCCESS', payload: user });
      } else {
        dispatch({ type: 'AUTH_FAILURE', payload: response.message || '註冊失敗' });
      }
    } catch (error: any) {
      dispatch({ type: 'AUTH_FAILURE', payload: error.message || '網絡錯誤，請稍後重試' });
    }
  };

  // 第三方登錄函數
  const thirdPartyLogin = async (platform: string, code: string) => {
    try {
      dispatch({ type: 'AUTH_START' });
      const response = await authService.thirdPartyLogin(platform, code);
      
      if (response.code === 200 && response.data) {
        const user = await authService.getUserProfile(response.data.userId);
        dispatch({ type: 'AUTH_SUCCESS', payload: user });
      } else {
        dispatch({ type: 'AUTH_FAILURE', payload: response.message || '第三方登錄失敗' });
      }
    } catch (error: any) {
      dispatch({ type: 'AUTH_FAILURE', payload: error.message || '網絡錯誤，請稍後重試' });
    }
  };

  // 登出函數
  const logout = () => {
    authService.logout();
    dispatch({ type: 'LOGOUT' });
  };

  // 清除錯誤
  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  // 更新用戶信息
  const updateUser = (userData: Partial<User>) => {
    dispatch({ type: 'UPDATE_USER', payload: userData });
  };

  const contextValue: AuthContextType = {
    state,
    login,
    register,
    logout,
    thirdPartyLogin,
    clearError,
    updateUser,
    checkAuthStatus,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom Hook
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth必須在AuthProvider內部使用');
  }
  return context;
}; 