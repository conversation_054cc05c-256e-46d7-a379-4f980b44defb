import React, { useState, useEffect, Suspense } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Star, 
  Eye, 
  Calendar,
  DollarSign,
  TrendingUp,
  Award,
  User,
  Clock,
  Building,
  Play,
  ExternalLink,
  Share2
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import MobileLayout from '@/components/layout/MobileLayout';
import { View, Common } from '@/components/canvas/View';
import { Progress3D } from '@/components/canvas/Progress3D';
import { ParticleSystem } from '@/components/canvas/ParticleSystem';

// 案例詳情數據類型
interface CaseDetail {
  id: number;
  title: string;
  subtitle?: string;
  description: string;
  detailed_description?: string;
  technologies?: string[];
  category: string;
  industry?: string;
  client_name?: string;
  project_duration?: string;
  project_budget?: number;
  achievements?: string[];
  benefits?: string;
  roi_percentage?: number;
  cover_image?: string;
  gallery_images?: string[];
  demo_video?: string;
  client_rating?: number;
  is_featured: boolean;
  view_count: number;
  testimonials: Array<{
    id: number;
    client_name: string;
    client_title?: string;
    client_company?: string;
    client_avatar?: string;
    content: string;
    rating: number;
    is_featured: boolean;
    created_at: string;
  }>;
  created_at: string;
  updated_at: string;
}

const CaseDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { state } = useAuth();
  
  const [caseDetail, setCaseDetail] = useState<CaseDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeImageIndex, setActiveImageIndex] = useState(0);

  useEffect(() => {
    if (id) {
      fetchCaseDetail(parseInt(id));
    }
  }, [id]);

  const fetchCaseDetail = async (caseId: number) => {
    setLoading(true);
    try {
      // 模擬 API 調用
      const mockDetail: CaseDetail = {
        id: caseId,
        title: "智慧醫療診斷系統",
        subtitle: "基於深度學習的醫學影像分析平台",
        description: "為某三甲醫院開發的AI輔助診斷系統，能夠快速準確地分析醫學影像，提高診斷效率和準確性。",
        detailed_description: "該系統採用最新的深度學習技術，結合大量醫學影像數據訓練，能夠識別多種疾病特徵。系統部署後，診斷準確率提升至95%以上，診斷時間縮短70%。系統支持多種醫學影像格式，包括CT、MRI、X光等，為醫生提供智能化的診斷輔助工具。",
        technologies: ["Deep Learning", "Computer Vision", "TensorFlow", "Medical AI", "Python", "DICOM"],
        category: "ai-vision",
        industry: "醫療健康",
        client_name: "某三甲醫院",
        project_duration: "6個月",
        project_budget: 500000,
        achievements: [
          "診斷準確率提升至95%以上",
          "診斷時間縮短70%",
          "減少誤診率60%",
          "提升醫生工作效率85%",
          "患者滿意度提升90%"
        ],
        benefits: "大幅提升醫院診斷效率，減少醫生工作負擔，提高患者滿意度。預計每年可節省成本200萬元，同時提升醫院整體醫療服務質量。",
        roi_percentage: 400,
        cover_image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800",
        gallery_images: [
          "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800",
          "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800",
          "https://images.unsplash.com/photo-**********-2a8555f1a136?w=800"
        ],
        demo_video: "https://example.com/demo-video.mp4",
        client_rating: 4.95,
        is_featured: true,
        view_count: 1250,
        testimonials: [
          {
            id: 1,
            client_name: "張主任",
            client_title: "影像科主任",
            client_company: "某三甲醫院",
            client_avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100",
            content: "這套AI診斷系統真的改變了我們的工作方式。診斷速度和準確性都有了質的飛躍，醫生們都很滿意。系統的智能化程度超出了我們的預期，大大提升了工作效率。",
            rating: 4.95,
            is_featured: true,
            created_at: "2024-01-20"
          },
          {
            id: 2,
            client_name: "李醫生",
            client_title: "放射科醫生",
            client_company: "某三甲醫院",
            content: "AI輔助診斷讓我們的工作更加精準，特別是在複雜病例的分析上，系統提供的建議非常有價值。",
            rating: 4.90,
            is_featured: false,
            created_at: "2024-01-25"
          }
        ],
        created_at: "2024-01-15",
        updated_at: "2024-01-20"
      };

      setCaseDetail(mockDetail);
    } catch (error) {
      console.error('獲取案例詳情失敗:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'ai-vision': 'bg-purple-100 text-purple-800',
      'ai-chat': 'bg-blue-100 text-blue-800',
      'ai-search': 'bg-green-100 text-green-800',
      'ai-media': 'bg-orange-100 text-orange-800',
      'ai-analytics': 'bg-red-100 text-red-800',
      'ai-recommendation': 'bg-yellow-100 text-yellow-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  const getCategoryName = (category: string) => {
    const names: Record<string, string> = {
      'ai-vision': '計算機視覺',
      'ai-chat': '對話系統',
      'ai-search': '智能搜索',
      'ai-media': '媒體處理',
      'ai-analytics': '數據分析',
      'ai-recommendation': '推薦系統'
    };
    return names[category] || category;
  };

  if (loading) {
    return (
      <MobileLayout enable3D={true}>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-500 mt-4">載入案例詳情...</p>
          </div>
        </div>
      </MobileLayout>
    );
  }

  if (!caseDetail) {
    return (
      <MobileLayout enable3D={true}>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <p className="text-gray-500">案例不存在</p>
            <button 
              onClick={() => navigate('/cases')}
              className="mt-4 px-4 py-2 bg-primary-600 text-white rounded-lg"
            >
              返回案例列表
            </button>
          </div>
        </div>
      </MobileLayout>
    );
  }

  return (
    <MobileLayout enable3D={true}>
      <div className="flex flex-col h-full bg-gray-50 relative">
        {/* 3D 背景效果 */}
        <View className="absolute inset-0 opacity-5">
          <Suspense fallback={null}>
            <ParticleSystem 
              count={100}
              color="#3b82f6"
              size={0.001}
              speed={0.015}
              spread={10}
            />
            <Common />
          </Suspense>
        </View>

        {/* 頁面頭部 */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 px-6 py-4 text-white relative z-10">
          <div className="flex items-center justify-between mb-4">
            <button 
              onClick={() => navigate('/cases')}
              className="p-2 rounded-lg bg-white/20 hover:bg-white/30 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div className="flex items-center space-x-2">
              <button className="p-2 rounded-lg bg-white/20 hover:bg-white/30 transition-colors">
                <Share2 className="w-5 h-5" />
              </button>
            </div>
          </div>

          <div className="flex items-start space-x-4">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <span className={`px-2 py-1 rounded-full text-xs ${getCategoryColor(caseDetail.category)} bg-white/20 text-white`}>
                  {getCategoryName(caseDetail.category)}
                </span>
                {caseDetail.is_featured && (
                  <Award className="w-4 h-4 text-yellow-300" />
                )}
              </div>
              <h1 className="text-xl font-bold mb-1">{caseDetail.title}</h1>
              {caseDetail.subtitle && (
                <p className="text-primary-100 text-sm">{caseDetail.subtitle}</p>
              )}
            </div>
          </div>

          {/* 統計信息 */}
          <div className="grid grid-cols-3 gap-4 mt-4 text-center">
            <div>
              <div className="text-lg font-bold">{caseDetail.client_rating?.toFixed(1)}</div>
              <div className="text-xs text-primary-200 flex items-center justify-center">
                <Star className="w-3 h-3 mr-1" />
                客戶評分
              </div>
            </div>
            <div>
              <div className="text-lg font-bold">{caseDetail.view_count}</div>
              <div className="text-xs text-primary-200 flex items-center justify-center">
                <Eye className="w-3 h-3 mr-1" />
                瀏覽次數
              </div>
            </div>
            <div>
              <div className="text-lg font-bold">{caseDetail.roi_percentage}%</div>
              <div className="text-xs text-primary-200 flex items-center justify-center">
                <TrendingUp className="w-3 h-3 mr-1" />
                投資回報
              </div>
            </div>
          </div>
        </div>

        {/* 內容區域 */}
        <div className="flex-1 overflow-y-auto relative z-10">
          {/* 項目圖片 */}
          {caseDetail.gallery_images && caseDetail.gallery_images.length > 0 && (
            <div className="px-6 py-4">
              <div className="bg-white rounded-lg overflow-hidden shadow-sm">
                <img 
                  src={caseDetail.gallery_images[activeImageIndex]} 
                  alt={caseDetail.title}
                  className="w-full h-48 object-cover"
                />
                {caseDetail.gallery_images.length > 1 && (
                  <div className="flex space-x-2 p-3 overflow-x-auto">
                    {caseDetail.gallery_images.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => setActiveImageIndex(index)}
                        className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 ${
                          index === activeImageIndex ? 'border-primary-500' : 'border-gray-200'
                        }`}
                      >
                        <img src={image} alt="" className="w-full h-full object-cover" />
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 項目信息 */}
          <div className="px-6 py-4 space-y-6">
            {/* 基本信息 */}
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <h2 className="text-lg font-semibold text-gray-900 mb-3">項目概述</h2>
              <p className="text-gray-600 mb-4">{caseDetail.description}</p>
              {caseDetail.detailed_description && (
                <p className="text-gray-600">{caseDetail.detailed_description}</p>
              )}
            </div>

            {/* 項目詳情 */}
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <h2 className="text-lg font-semibold text-gray-900 mb-3">項目詳情</h2>
              <div className="grid grid-cols-2 gap-4">
                {caseDetail.client_name && (
                  <div className="flex items-center space-x-2">
                    <Building className="w-4 h-4 text-gray-400" />
                    <div>
                      <div className="text-xs text-gray-500">客戶</div>
                      <div className="text-sm font-medium">{caseDetail.client_name}</div>
                    </div>
                  </div>
                )}
                {caseDetail.project_duration && (
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-gray-400" />
                    <div>
                      <div className="text-xs text-gray-500">週期</div>
                      <div className="text-sm font-medium">{caseDetail.project_duration}</div>
                    </div>
                  </div>
                )}
                {caseDetail.industry && (
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="w-4 h-4 text-gray-400" />
                    <div>
                      <div className="text-xs text-gray-500">行業</div>
                      <div className="text-sm font-medium">{caseDetail.industry}</div>
                    </div>
                  </div>
                )}
                {caseDetail.project_budget && (
                  <div className="flex items-center space-x-2">
                    <DollarSign className="w-4 h-4 text-gray-400" />
                    <div>
                      <div className="text-xs text-gray-500">預算</div>
                      <div className="text-sm font-medium">¥{caseDetail.project_budget.toLocaleString()}</div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* 技術棧 */}
            {caseDetail.technologies && caseDetail.technologies.length > 0 && (
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <h2 className="text-lg font-semibold text-gray-900 mb-3">技術棧</h2>
                <div className="flex flex-wrap gap-2">
                  {caseDetail.technologies.map((tech, index) => (
                    <span 
                      key={index}
                      className="px-3 py-1 bg-primary-100 text-primary-800 rounded-full text-sm"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* 項目成果 */}
            {caseDetail.achievements && caseDetail.achievements.length > 0 && (
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <h2 className="text-lg font-semibold text-gray-900 mb-3">項目成果</h2>
                <div className="space-y-3">
                  {caseDetail.achievements.map((achievement, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <div className="w-16 h-16">
                        <View className="w-full h-full">
                          <Suspense fallback={null}>
                            <Progress3D
                              progress={85 + index * 5}
                              size={0.3}
                              color="#10b981"
                              position={[0, 0, 0]}
                              animated={true}
                            />
                            <Common />
                          </Suspense>
                        </View>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">{achievement}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 項目效益 */}
            {caseDetail.benefits && (
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <h2 className="text-lg font-semibold text-gray-900 mb-3">項目效益</h2>
                <p className="text-gray-600">{caseDetail.benefits}</p>
              </div>
            )}

            {/* 客戶評價 */}
            {caseDetail.testimonials && caseDetail.testimonials.length > 0 && (
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <h2 className="text-lg font-semibold text-gray-900 mb-3">客戶評價</h2>
                <div className="space-y-4">
                  {caseDetail.testimonials.map((testimonial) => (
                    <div key={testimonial.id} className="border-l-4 border-primary-500 pl-4">
                      <div className="flex items-start space-x-3">
                        <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
                          {testimonial.client_avatar ? (
                            <img 
                              src={testimonial.client_avatar} 
                              alt={testimonial.client_name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <User className="w-5 h-5 text-gray-400" />
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="font-medium text-sm">{testimonial.client_name}</span>
                            {testimonial.client_title && (
                              <span className="text-xs text-gray-500">• {testimonial.client_title}</span>
                            )}
                            <div className="flex items-center">
                              <Star className="w-3 h-3 text-yellow-400 mr-1" />
                              <span className="text-xs">{testimonial.rating.toFixed(1)}</span>
                            </div>
                          </div>
                          {testimonial.client_company && (
                            <p className="text-xs text-gray-500 mb-2">{testimonial.client_company}</p>
                          )}
                          <p className="text-sm text-gray-600">{testimonial.content}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </MobileLayout>
  );
};

export default CaseDetailPage;
