<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI速應 - RAG聊天机器人服务</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://unpkg.com/lucide-static/font/lucide.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f7fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            padding-bottom: 20px;
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }
        .header {
            position: relative;
        }
        .back-button {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 36px;
            height: 36px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .share-button {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 36px;
            height: 36px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .tag {
            background: rgba(99, 102, 241, 0.1);
            color: #6366f1;
            border-radius: 6px;
            padding: 2px 8px;
            font-size: 12px;
            font-weight: 500;
        }
        .tab {
            padding: 10px 0;
            font-size: 14px;
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            color: #6366f1;
            border-bottom: 2px solid #6366f1;
            font-weight: 500;
        }
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .feature-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: rgba(99, 102, 241, 0.1);
            color: #6366f1;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
        }
        .bottom-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 100;
        }
    </style>
</head>
<body>
    <!-- 顶部图片 -->
    <div class="header">
        <div class="back-button">
            <i class="lucide lucide-chevron-left"></i>
        </div>
        <div class="share-button">
            <i class="lucide lucide-share-2"></i>
        </div>
        <img src="./images/rag.png" alt="RAG AI" class="w-full h-56 object-cover">
    </div>

    <!-- 服务信息 -->
    <div class="p-4">
        <div class="flex justify-between items-start mb-2">
            <h1 class="text-xl font-bold">RAG聊天机器人服务</h1>
            <div class="flex items-center text-yellow-500 text-sm">
                <i class="lucide lucide-star-filled"></i>
                <span class="ml-1">4.8 (256)</span>
            </div>
        </div>
        <div class="flex space-x-2 mb-4">
            <span class="tag">智能检索</span>
            <span class="tag">知识库</span>
            <span class="tag">高精度</span>
        </div>
        <div class="flex justify-between items-center mb-6">
            <div>
                <span class="text-2xl font-bold text-indigo-600">¥1999</span>
                <span class="text-gray-500 line-through ml-2">¥2999</span>
            </div>
            <div class="text-sm text-gray-500">已售 256 | 好评率 97%</div>
        </div>

        <!-- 服务标签 -->
        <div class="flex justify-between border-b mb-4">
            <div class="tab active text-center flex-1">服务详情</div>
            <div class="tab text-gray-600 text-center flex-1">客户评价</div>
            <div class="tab text-gray-600 text-center flex-1">常见问题</div>
        </div>

        <!-- 服务详情 -->
        <div>
            <div class="glass-card p-4 mb-4">
                <h3 class="font-semibold mb-3">服务介绍</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    RAG（检索增强生成）聊天机器人服务为企业提供基于最新AI技术的智能问答系统，通过结合企业专有知识库与大语言模型，实现精准的信息检索和自然流畅的对话体验。我们的服务包括知识库构建、模型训练、系统集成和持续优化，帮助企业提升客户服务质量和内部知识管理效率。
                </p>
            </div>

            <div class="glass-card p-4 mb-4">
                <h3 class="font-semibold mb-3">服务内容</h3>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="lucide lucide-database"></i>
                    </div>
                    <div>
                        <h4 class="font-medium">知识库构建</h4>
                        <p class="text-gray-600 text-sm">导入企业文档，构建高质量向量数据库</p>
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="lucide lucide-search"></i>
                    </div>
                    <div>
                        <h4 class="font-medium">智能检索引擎</h4>
                        <p class="text-gray-600 text-sm">基于语义的高精度信息检索系统</p>
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="lucide lucide-message-square"></i>
                    </div>
                    <div>
                        <h4 class="font-medium">对话生成优化</h4>
                        <p class="text-gray-600 text-sm">结合检索结果生成自然流畅的回答</p>
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="lucide lucide-plug"></i>
                    </div>
                    <div>
                        <h4 class="font-medium">多平台集成</h4>
                        <p class="text-gray-600 text-sm">支持网站、APP、微信等多平台接入</p>
                    </div>
                </div>
            </div>

            <div class="glass-card p-4 mb-4">
                <h3 class="font-semibold mb-3">RAG技术架构</h3>
                <div class="flex flex-col items-center mb-4">
                    <div class="w-full flex justify-center my-3">
                        <img src="./images/rag.png" alt="RAG技术架构图" class="w-full max-w-xs rounded-lg shadow-sm">
                    </div>
                </div>
                <p class="text-gray-600 text-sm leading-relaxed mb-3">
                    RAG（检索增强生成）技术将传统检索系统与生成式AI模型相结合，通过先检索相关信息，再基于检索结果生成回答，大幅提升AI回答的准确性和可靠性。这种方法特别适合需要专业领域知识支持的场景，能有效减少幻觉问题，提供有据可查的回答。
                </p>
                <div class="flex flex-wrap gap-2 mt-2">
                    <span class="tag bg-blue-50 text-blue-600">高精度</span>
                    <span class="tag bg-green-50 text-green-600">可靠性</span>
                    <span class="tag bg-purple-50 text-purple-600">实时更新</span>
                    <span class="tag bg-orange-50 text-orange-600">可解释性</span>
                </div>
            </div>

            <div class="glass-card p-4 mb-4">
                <h3 class="font-semibold mb-3">适用场景</h3>
                <div class="grid grid-cols-2 gap-3">
                    <div class="bg-indigo-50 p-3 rounded-lg">
                        <div class="flex items-center mb-2">
                            <i class="lucide lucide-message-circle text-indigo-600 mr-2"></i>
                            <span class="font-medium">客户服务</span>
                        </div>
                        <p class="text-gray-600 text-xs">智能回答客户常见问题，提升服务效率</p>
                    </div>
                    <div class="bg-indigo-50 p-3 rounded-lg">
                        <div class="flex items-center mb-2">
                            <i class="lucide lucide-book-open text-indigo-600 mr-2"></i>
                            <span class="font-medium">知识管理</span>
                        </div>
                        <p class="text-gray-600 text-xs">企业内部知识库智能检索与问答</p>
                    </div>
                    <div class="bg-indigo-50 p-3 rounded-lg">
                        <div class="flex items-center mb-2">
                            <i class="lucide lucide-graduation-cap text-indigo-600 mr-2"></i>
                            <span class="font-medium">培训辅助</span>
                        </div>
                        <p class="text-gray-600 text-xs">新员工培训与学习辅助工具</p>
                    </div>
                    <div class="bg-indigo-50 p-3 rounded-lg">
                        <div class="flex items-center mb-2">
                            <i class="lucide lucide-shopping-cart text-indigo-600 mr-2"></i>
                            <span class="font-medium">销售支持</span>
                        </div>
                        <p class="text-gray-600 text-xs">产品咨询与智能推荐服务</p>
                    </div>
                </div>
            </div>

            <div class="glass-card p-4 mb-4">
                <h3 class="font-semibold mb-3">服务流程</h3>
                <div class="relative pl-8 pb-1">
                    <div class="absolute left-3 top-0 bottom-0 w-0.5 bg-indigo-100"></div>
                    <div class="absolute left-0 top-0 w-6 h-6 rounded-full bg-indigo-600 flex items-center justify-center text-white text-xs">1</div>
                    <div class="mb-4">
                        <h4 class="font-medium">需求分析</h4>
                        <p class="text-gray-600 text-sm">了解业务场景与知识库需求</p>
                    </div>
                </div>
                <div class="relative pl-8 pb-1">
                    <div class="absolute left-3 top-0 bottom-0 w-0.5 bg-indigo-100"></div>
                    <div class="absolute left-0 top-0 w-6 h-6 rounded-full bg-indigo-600 flex items-center justify-center text-white text-xs">2</div>
                    <div class="mb-4">
                        <h4 class="font-medium">知识库构建</h4>
                        <p class="text-gray-600 text-sm">导入文档并构建向量数据库</p>
                    </div>
                </div>
                <div class="relative pl-8 pb-1">
                    <div class="absolute left-3 top-0 bottom-0 w-0.5 bg-indigo-100"></div>
                    <div class="absolute left-0 top-0 w-6 h-6 rounded-full bg-indigo-600 flex items-center justify-center text-white text-xs">3</div>
                    <div class="mb-4">
                        <h4 class="font-medium">系统开发</h4>
                        <p class="text-gray-600 text-sm">RAG模型训练与对话系统开发</p>
                    </div>
                </div>
                <div class="relative pl-8">
                    <div class="absolute left-0 top-0 w-6 h-6 rounded-full bg-indigo-600 flex items-center justify-center text-white text-xs">4</div>
                    <div class="mb-1">
                        <h4 class="font-medium">部署与优化</h4>
                        <p class="text-gray-600 text-sm">系统上线与持续优化服务</p>
                    </div>
                </div>
            </div>

            <!-- 成功案例 -->
            <div class="glass-card p-4 mb-20">
                <h3 class="font-semibold mb-3">成功案例</h3>
                <div class="mb-4 pb-4 border-b border-gray-100">
                    <div class="flex items-center mb-2">
                        <img src="https://images.unsplash.com/photo-1568992687947-868a62a9f521?q=80&w=50&auto=format&fit=crop" class="w-8 h-8 rounded-full mr-2" alt="客户头像">
                        <h4 class="font-medium">某电商平台</h4>
                    </div>
                    <p class="text-gray-600 text-sm mb-2">通过RAG聊天机器人服务，客服响应时间缩短70%，客户满意度提升35%，大幅降低了人工客服成本。</p>
                    <div class="flex items-center text-yellow-500 text-xs">
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-filled"></i>
                    </div>
                </div>
                <div>
                    <div class="flex items-center mb-2">
                        <img src="https://images.unsplash.com/photo-1507679799987-c73779587ccf?q=80&w=50&auto=format&fit=crop" class="w-8 h-8 rounded-full mr-2" alt="客户头像">
                        <h4 class="font-medium">某教育机构</h4>
                    </div>
                    <p class="text-gray-600 text-sm mb-2">部署RAG知识库助手后，学生问题解答准确率达95%，教师工作效率提升40%，极大改善了学习体验。</p>
                    <div class="flex items-center text-yellow-500 text-xs">
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-filled"></i>
                        <i class="lucide lucide-star-half"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部购买栏 -->
    <div class="bottom-bar">
        <div class="flex items-center">
            <div class="mr-4 text-center">
                <i class="lucide lucide-message-circle text-gray-600"></i>
                <div class="text-xs text-gray-600">咨询</div>
            </div>
            <div class="text-center">
                <i class="lucide lucide-heart text-gray-600"></i>
                <div class="text-xs text-gray-600">收藏</div>
            </div>
        </div>
        <div class="flex">
            <button class="px-4 py-2 bg-indigo-100 text-indigo-600 rounded-l-full font-medium">免费咨询</button>
            <button class="px-4 py-2 bg-indigo-600 text-white rounded-r-full font-medium">立即购买</button>
        </div>
    </div>
</body>
</html>