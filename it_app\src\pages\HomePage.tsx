import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Plus, 
  Briefcase, 
  TrendingUp, 
  Star,
  ArrowRight,
  Brain,
  Image,
  BarChart3,
  MessageSquare,
  Bot,
  Search
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import MobileLayout from '@/components/layout/MobileLayout';
import Button from '@/components/ui/Button';

// 服務類型數據
const serviceTypes = [
  {
    id: 1,
    name: 'AI圖像識別',
    icon: Image,
    description: '圖像分類、物體檢測',
    color: 'bg-blue-100 text-blue-600',
    cases: 24
  },
  {
    id: 2,
    name: '自然語言處理',
    icon: MessageSquare,
    description: '文本分析、情感識別',
    color: 'bg-green-100 text-green-600',
    cases: 18
  },
  {
    id: 3,
    name: '數據分析',
    icon: BarChart3,
    description: '預測分析、統計建模',
    color: 'bg-purple-100 text-purple-600',
    cases: 15
  },
  {
    id: 4,
    name: '智能客服',
    icon: Bo<PERSON>,
    description: '聊天機器人、自動回復',
    color: 'bg-orange-100 text-orange-600',
    cases: 12
  }
];

// 成功案例數據
const successCases = [
  {
    id: 1,
    title: '電商圖像識別系統',
    category: 'AI圖像識別',
    rating: 5.0,
    reviews: 23,
    price: '¥8,500',
    duration: '7天',
    tags: ['深度學習', 'CNN', 'TensorFlow']
  },
  {
    id: 2,
    title: '金融風控模型',
    category: '數據分析',
    rating: 4.9,
    reviews: 18,
    price: '¥12,000',
    duration: '14天',
    tags: ['機器學習', '風險評估', 'Python']
  },
  {
    id: 3,
    title: '智能問答機器人',
    category: '自然語言處理',
    rating: 4.8,
    reviews: 31,
    price: '¥6,800',
    duration: '10天',
    tags: ['NLP', '對話系統', 'BERT']
  }
];

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const { state } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');

  // 檢查認證狀態
  useEffect(() => {
    if (!state.isAuthenticated) {
      navigate('/login');
    }
  }, [state.isAuthenticated, navigate]);

  const handleSubmitCase = () => {
    navigate('/cases/submit');
  };

  const handleServiceTypeClick = (serviceType: typeof serviceTypes[0]) => {
    navigate(`/cases?category=${encodeURIComponent(serviceType.name)}`);
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      navigate(`/cases?search=${encodeURIComponent(searchQuery)}`);
    }
  };

  if (!state.isAuthenticated) {
    return null; // 避免閃爍
  }

  return (
    <MobileLayout>
      <div className="flex flex-col h-full bg-gray-50">
        {/* 頭部歡迎區域 */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 px-6 py-6 text-white">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold">
                您好，{state.user?.nickname || state.user?.username}
              </h1>
              <p className="text-primary-100 mt-1">
                今天有什麼AI項目需要幫助嗎？
              </p>
            </div>
            <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
              <Brain className="w-6 h-6" />
            </div>
          </div>

          {/* 搜索框 */}
          <div className="relative">
            <input
              type="text"
              placeholder="搜索AI服務、案例或專家..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className="w-full px-4 py-3 pl-12 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/30"
            />
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/70" />
            <button
              onClick={handleSearch}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 rounded-md bg-white/20 hover:bg-white/30 transition-colors"
            >
              <ArrowRight className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* 快速操作按鈕 */}
        <div className="px-6 -mt-6 mb-6">
          <div className="bg-white rounded-2xl shadow-lg p-4">
            <div className="grid grid-cols-2 gap-4">
              <Button
                onClick={handleSubmitCase}
                className="flex flex-col items-center py-4 h-auto"
                size="lg"
              >
                <Plus className="w-6 h-6 mb-2" />
                <span>提交案件需求</span>
              </Button>
              <Button
                variant="outline"
                onClick={() => navigate('/cases')}
                className="flex flex-col items-center py-4 h-auto"
                size="lg"
              >
                <Briefcase className="w-6 h-6 mb-2" />
                <span>瀏覽案件</span>
              </Button>
            </div>
          </div>
        </div>

        {/* 主要內容 */}
        <div className="flex-1 px-6 space-y-6">
          {/* 熱門服務類型 */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-gray-900">熱門服務類型</h2>
              <button 
                onClick={() => navigate('/cases')}
                className="text-primary-600 font-medium flex items-center"
              >
                更多 <ArrowRight className="w-4 h-4 ml-1" />
              </button>
            </div>
            
            <div className="grid grid-cols-2 gap-3">
              {serviceTypes.map((service) => {
                const Icon = service.icon;
                return (
                  <button
                    key={service.id}
                    onClick={() => handleServiceTypeClick(service)}
                    className="bg-white rounded-xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200 active:scale-95"
                  >
                    <div className={`w-12 h-12 rounded-lg ${service.color} flex items-center justify-center mb-3`}>
                      <Icon className="w-6 h-6" />
                    </div>
                    <h3 className="font-semibold text-gray-900 text-sm mb-1">
                      {service.name}
                    </h3>
                    <p className="text-xs text-gray-500 mb-2">
                      {service.description}
                    </p>
                    <div className="flex items-center text-xs text-gray-400">
                      <TrendingUp className="w-3 h-3 mr-1" />
                      {service.cases} 個案件
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          {/* 精選成功案例 */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-gray-900">精選成功案例</h2>
              <button 
                onClick={() => navigate('/cases?filter=success')}
                className="text-primary-600 font-medium flex items-center"
              >
                更多 <ArrowRight className="w-4 h-4 ml-1" />
              </button>
            </div>

            <div className="space-y-4">
              {successCases.map((caseItem) => (
                <div
                  key={caseItem.id}
                  className="bg-white rounded-xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200"
                >
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-1">
                        {caseItem.title}
                      </h3>
                      <p className="text-sm text-primary-600 mb-2">
                        {caseItem.category}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-gray-900">
                        {caseItem.price}
                      </div>
                      <div className="text-xs text-gray-500">
                        {caseItem.duration}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center mb-3">
                    <div className="flex items-center mr-4">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="text-sm font-medium text-gray-700 ml-1">
                        {caseItem.rating}
                      </span>
                      <span className="text-sm text-gray-500 ml-1">
                        ({caseItem.reviews})
                      </span>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    {caseItem.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-md"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 底部空白，避免被導航欄遮擋 */}
        <div className="h-4"></div>
      </div>
    </MobileLayout>
  );
};

export default HomePage; 