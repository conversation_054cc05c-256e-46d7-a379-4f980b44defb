---
description: 
globs: 
alwaysApply: false
---
# 用户故事编写规范

**ID:** [ID]
**标题:** [精准描述该故事的核心价值]
**优先级:**
    (Priority - 基于第一性原理)[e.g., 根本核心 (P0) / 重要支撑 (P1) / 体验优化 (P2) / 可选探索 (P3)]

**用户角色 (作为):** [具体的、有代表性的用户画像角色]
**期望行为 (我希望):** [清晰描述用户期望完成的*任务*或达成的*状态*，而非具体操作界面]
**目标/价值 (以便):** [这能满足用户的哪项*根本需求*或解决哪个*核心痛点*，带来什么*真实价值*]

**验收标准:**
 (Acceptance Criteria - 基于价值的验收标准)

1. [标准1: 清晰、可衡量、可测试的*结果*，证明用户价值已实现]
2. [标准2: 涉及的关键业务规则或约束条件]
3. [标准3: 边缘情况或异常处理的基本要求]
4. [补充负面/异常场景]
   ...

**技术要求：**

- [实现约束，不提前指定技术选型，除非有业务强约束]
- [安全、性能、兼容性等要求]
