"""
通用 Pydantic 模型
"""

from typing import Any, Generic, List, Optional, TypeVar
from pydantic import BaseModel, Field

T = TypeVar('T')


class ResponseModel(BaseModel):
    """標準 API 響應模型"""
    
    success: bool = Field(description="請求是否成功")
    message: str = Field(description="響應消息")
    data: Optional[Any] = Field(default=None, description="響應數據")
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "操作成功",
                "data": None
            }
        }


class PaginationParams(BaseModel):
    """分頁參數模型"""
    
    page: int = Field(default=1, ge=1, description="頁碼")
    size: int = Field(default=20, ge=1, le=100, description="每頁數量")
    
    @property
    def offset(self) -> int:
        """計算偏移量"""
        return (self.page - 1) * self.size


class PaginatedResponse(BaseModel, Generic[T]):
    """分頁響應模型"""
    
    items: List[T] = Field(description="數據列表")
    total: int = Field(description="總數量")
    page: int = Field(description="當前頁碼")
    size: int = Field(description="每頁數量")
    pages: int = Field(description="總頁數")
    
    @classmethod
    def create(
        cls, 
        items: List[T], 
        total: int, 
        page: int, 
        size: int
    ) -> "PaginatedResponse[T]":
        """創建分頁響應"""
        pages = (total + size - 1) // size  # 向上取整
        return cls(
            items=items,
            total=total,
            page=page,
            size=size,
            pages=pages
        )
