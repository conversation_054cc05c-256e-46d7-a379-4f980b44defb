"""
FastAPI 依賴注入
"""

from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.models.user import User
from app.services.auth_service import AuthService
from app.services.user_service import UserService
from app.services.email_service import EmailService

# HTTP Bearer 認證方案
security = HTTPBearer(auto_error=False)


async def get_auth_service(db: AsyncSession = Depends(get_db)) -> AuthService:
    """獲取認證服務實例"""
    return AuthService(db)


async def get_user_service(db: AsyncSession = Depends(get_db)) -> UserService:
    """獲取用戶服務實例"""
    return UserService(db)


async def get_email_service() -> EmailService:
    """獲取郵件服務實例"""
    return EmailService()


async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    auth_service: AuthService = Depends(get_auth_service)
) -> User:
    """
    獲取當前認證用戶
    
    Args:
        credentials: HTTP Bearer 憑據
        auth_service: 認證服務
        
    Returns:
        User: 當前用戶對象
        
    Raises:
        HTTPException: 認證失敗
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未提供認證令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user = await auth_service.get_user_by_token(credentials.credentials)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="無效的認證令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    獲取當前活躍用戶
    
    Args:
        current_user: 當前用戶
        
    Returns:
        User: 活躍用戶對象
        
    Raises:
        HTTPException: 用戶已停用
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用戶帳號已停用"
        )
    
    return current_user


# 可選的認證依賴（不強制要求認證）
async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    auth_service: AuthService = Depends(get_auth_service)
) -> Optional[User]:
    """
    獲取當前用戶（可選）
    
    Args:
        credentials: HTTP Bearer 憑據
        auth_service: 認證服務
        
    Returns:
        User: 當前用戶對象或 None
    """
    if not credentials:
        return None
    
    user = await auth_service.get_user_by_token(credentials.credentials)
    return user if user and user.is_active else None
