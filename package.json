{"name": "react-three-next", "version": "2.0.0", "authors": ["Renaud ROHLINGER <https://twitter.com/onirenaud>"], "license": "MIT", "private": true, "engines": {"node": ">=14"}, "scripts": {"lint": "next lint --fix --dir app", "dev": "next dev", "build": "next build", "analyze": "ANALYZE=true next build", "start": "next start"}, "dependencies": {"@ducanh2912/next-pwa": "^10.0.0", "@headlessui/react": "^2.0.3", "@heroicons/react": "^2.1.3", "@react-three/drei": "^9.92.7", "@react-three/fiber": "^8.15.12", "glsl-random": "^0.0.5", "next": "^14.0.4", "next-themes": "^0.3.0", "r3f-globe": "^1.3.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.51.4", "three": "^0.176.0", "three-custom-shader-material": "^6.3.5", "three-globe": "^2.42.4", "three-stdlib": "^2.28.9", "tunnel-rat": "^0.1.2", "zustand": "^5.0.4"}, "devDependencies": {"@next/bundle-analyzer": "^14.0.4", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-tailwindcss": "^3.13.0", "file-loader": "^6.2.0", "glslify": "^7.1.1", "glslify-loader": "^2.0.0", "postcss": "^8.4.32", "prettier": "^3.1.1", "raw-loader": "^4.0.2", "tailwindcss": "^3.4.0", "url-loader": "^4.1.1"}}