# AI速應後端 API 系統

## 📋 項目概述

AI速應後端 API 是一個基於 FastAPI 的現代化後端系統，為 3D 沉浸式前端應用提供完整的後端服務支持。

### 🎯 主要功能

- **用戶認證系統**: 支持郵箱密碼登入、郵箱驗證碼登入、LINE 登入
- **用戶管理**: 個人資料管理、密碼修改、頭像上傳
- **AI 服務管理**: 服務列表、詳情查詢、分類篩選
- **安全機制**: JWT 認證、密碼加密、API 速率限制
- **數據庫**: SQLite 數據庫，支持異步操作

### 🔧 技術棧

- **後端框架**: FastAPI 0.104+
- **數據庫**: SQLite 3 + SQLAlchemy 2.0 (async)
- **認證**: JWT (python-jose) + bcrypt
- **數據驗證**: Pydantic v2
- **API 文檔**: 自動生成 OpenAPI/Swagger

## 🚀 快速開始

### 環境要求

- Python 3.11+
- pip 或 poetry

### 安裝步驟

1. **創建虛擬環境**
```bash
cd it_app/backend
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

2. **安裝依賴**
```bash
pip install -r requirements.txt
```

3. **配置環境變量**
```bash
cp .env.example .env
# 編輯 .env 文件，配置必要的環境變量
```

4. **啟動開發服務器**
```bash
python run.py
```

5. **訪問 API 文檔**
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 📁 項目結構

```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI 應用入口
│   ├── api/                 # API 路由
│   │   ├── __init__.py
│   │   ├── auth.py          # 認證 API
│   │   ├── users.py         # 用戶管理 API
│   │   └── services.py      # AI 服務 API
│   ├── core/                # 核心功能
│   │   ├── __init__.py
│   │   ├── config.py        # 配置管理
│   │   ├── security.py      # 安全功能
│   │   ├── database.py      # 數據庫配置
│   │   └── dependencies.py  # 依賴注入
│   ├── models/              # 數據模型
│   │   ├── __init__.py
│   │   ├── base.py          # 基礎模型
│   │   ├── user.py          # 用戶模型
│   │   └── service.py       # 服務模型
│   ├── schemas/             # 數據驗證
│   │   ├── __init__.py
│   │   ├── auth.py          # 認證 Schema
│   │   ├── user.py          # 用戶 Schema
│   │   └── common.py        # 通用 Schema
│   └── services/            # 業務邏輯
│       ├── __init__.py
│       ├── auth_service.py  # 認證服務
│       ├── user_service.py  # 用戶服務
│       └── email_service.py # 郵件服務
├── tests/                   # 測試文件
├── requirements.txt         # 依賴列表
├── .env.example            # 環境變量示例
├── .env                    # 環境變量配置
└── run.py                  # 啟動腳本
```

## 🔌 API 端點

### 認證相關 (/api/auth)

| 方法 | 端點 | 描述 |
|------|------|------|
| POST | `/api/auth/register` | 用戶註冊 |
| POST | `/api/auth/login` | 郵箱密碼登入 |
| POST | `/api/auth/email-code/send` | 發送郵箱驗證碼 |
| POST | `/api/auth/email-code/verify` | 驗證碼登入 |
| GET | `/api/auth/me` | 獲取當前用戶信息 |
| POST | `/api/auth/logout` | 用戶登出 |

### 用戶管理 (/api/users)

| 方法 | 端點 | 描述 |
|------|------|------|
| GET | `/api/users/me` | 獲取用戶資料 |
| PUT | `/api/users/me` | 更新用戶資料 |
| PUT | `/api/users/me/password` | 修改密碼 |
| POST | `/api/users/me/avatar` | 上傳頭像 |

### AI 服務 (/api/services)

| 方法 | 端點 | 描述 |
|------|------|------|
| GET | `/api/services` | 獲取服務列表 |
| GET | `/api/services/{id}` | 獲取服務詳情 |
| GET | `/api/services/categories/list` | 獲取分類列表 |

## 🗄️ 數據庫設計

### 用戶表 (users)
- id: 主鍵
- username: 用戶名
- email: 電子郵件
- password_hash: 加密密碼
- nickname: 暱稱
- avatar_url: 頭像 URL
- is_active: 帳號狀態
- login_provider: 登入方式
- created_at/updated_at: 時間戳

### AI 服務表 (ai_services)
- id: 主鍵
- name: 服務名稱
- description: 服務描述
- category: 服務分類
- price_min/price_max: 價格範圍
- is_active: 是否啟用
- created_at: 創建時間

## 🔒 安全特性

- **JWT 認證**: 訪問令牌 1 小時，刷新令牌 7 天
- **密碼加密**: bcrypt 加密，cost factor = 12
- **CORS 配置**: 允許前端域名跨域訪問
- **輸入驗證**: Pydantic 數據驗證
- **錯誤處理**: 統一錯誤響應格式

## 🧪 測試

```bash
# 運行測試
pytest

# 運行測試並生成覆蓋率報告
pytest --cov=app tests/
```

## 📦 部署

### Docker 部署

```bash
# 構建鏡像
docker build -t ai-speed-backend .

# 運行容器
docker run -p 8000:8000 ai-speed-backend
```

### 生產環境配置

1. 設置環境變量 `ENVIRONMENT=production`
2. 配置強密鑰 `SECRET_KEY`
3. 配置郵件服務器
4. 設置 HTTPS
5. 配置反向代理 (Nginx)

## 🤝 與前端集成

### CORS 配置
```python
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com
```

### API 響應格式
```json
{
  "success": true,
  "message": "操作成功",
  "data": {...}
}
```

### 認證頭部
```
Authorization: Bearer <access_token>
```

## 📝 開發指南

### 添加新的 API 端點

1. 在 `app/schemas/` 中定義數據模型
2. 在 `app/services/` 中實現業務邏輯
3. 在 `app/api/` 中創建路由
4. 在 `app/api/__init__.py` 中註冊路由
5. 編寫測試

### 數據庫遷移

```bash
# 生成遷移文件
alembic revision --autogenerate -m "描述"

# 執行遷移
alembic upgrade head
```

## 🐛 故障排除

### 常見問題

1. **數據庫連接失敗**: 檢查 `DATABASE_URL` 配置
2. **JWT 令牌無效**: 檢查 `SECRET_KEY` 配置
3. **CORS 錯誤**: 檢查 `ALLOWED_ORIGINS` 配置
4. **郵件發送失敗**: 檢查 SMTP 配置

### 日誌查看

```bash
# 查看應用日誌
tail -f app.log

# 調試模式
DEBUG=True python run.py
```

## 📞 支持

如有問題，請聯繫開發團隊或查看項目文檔。
