"""
安全相關功能
包含 JWT 令牌處理、密碼加密等
"""

from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Union
import secrets
import string

from jose import JWTError, jwt
from passlib.context import CryptContext

from .config import get_settings

settings = get_settings()

# 密碼加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def create_access_token(
    subject: Union[str, Any], 
    expires_delta: Optional[timedelta] = None
) -> str:
    """
    創建 JWT 訪問令牌
    
    Args:
        subject: 令牌主體（通常是用戶ID）
        expires_delta: 過期時間增量
        
    Returns:
        JWT 令牌字符串
    """
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.access_token_expire_minutes
        )
    
    to_encode = {
        "exp": expire,
        "sub": str(subject),
        "type": "access"
    }
    
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.secret_key, 
        algorithm=settings.algorithm
    )
    return encoded_jwt


def create_refresh_token(
    subject: Union[str, Any], 
    expires_delta: Optional[timedelta] = None
) -> str:
    """
    創建 JWT 刷新令牌
    
    Args:
        subject: 令牌主體（通常是用戶ID）
        expires_delta: 過期時間增量
        
    Returns:
        JWT 刷新令牌字符串
    """
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            days=settings.refresh_token_expire_days
        )
    
    to_encode = {
        "exp": expire,
        "sub": str(subject),
        "type": "refresh"
    }
    
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.secret_key, 
        algorithm=settings.algorithm
    )
    return encoded_jwt


def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """
    驗證 JWT 令牌
    
    Args:
        token: JWT 令牌字符串
        
    Returns:
        令牌載荷或 None
    """
    try:
        payload = jwt.decode(
            token, 
            settings.secret_key, 
            algorithms=[settings.algorithm]
        )
        return payload
    except JWTError:
        return None


def get_password_hash(password: str) -> str:
    """
    生成密碼哈希
    
    Args:
        password: 明文密碼
        
    Returns:
        哈希後的密碼
    """
    return pwd_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    驗證密碼
    
    Args:
        plain_password: 明文密碼
        hashed_password: 哈希密碼
        
    Returns:
        密碼是否匹配
    """
    return pwd_context.verify(plain_password, hashed_password)


def generate_verification_code(length: int = 6) -> str:
    """
    生成驗證碼
    
    Args:
        length: 驗證碼長度
        
    Returns:
        數字驗證碼
    """
    return ''.join(secrets.choice(string.digits) for _ in range(length))


def generate_random_string(length: int = 32) -> str:
    """
    生成隨機字符串
    
    Args:
        length: 字符串長度
        
    Returns:
        隨機字符串
    """
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))
