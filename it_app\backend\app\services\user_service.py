"""
用戶業務邏輯服務
"""

from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from fastapi import HTT<PERSON>Ex<PERSON>, status

from app.core.security import get_password_hash, verify_password
from app.models.user import User
from app.schemas.user import UserUpdate, PasswordUpdate


class UserService:
    """用戶服務類"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_user_by_id(self, user_id: int) -> Optional[User]:
        """
        根據 ID 獲取用戶
        
        Args:
            user_id: 用戶 ID
            
        Returns:
            User: 用戶對象或 None
        """
        stmt = select(User).where(User.id == user_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """
        根據郵箱獲取用戶
        
        Args:
            email: 郵箱地址
            
        Returns:
            User: 用戶對象或 None
        """
        stmt = select(User).where(User.email == email)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def update_user(self, user: User, user_data: UserUpdate) -> User:
        """
        更新用戶信息
        
        Args:
            user: 用戶對象
            user_data: 更新數據
            
        Returns:
            User: 更新後的用戶對象
        """
        update_data = user_data.model_dump(exclude_unset=True)
        
        for field, value in update_data.items():
            setattr(user, field, value)
        
        await self.db.commit()
        await self.db.refresh(user)
        
        return user
    
    async def update_password(
        self, 
        user: User, 
        password_data: PasswordUpdate
    ) -> User:
        """
        更新用戶密碼
        
        Args:
            user: 用戶對象
            password_data: 密碼更新數據
            
        Returns:
            User: 更新後的用戶對象
            
        Raises:
            HTTPException: 當前密碼錯誤
        """
        # 驗證當前密碼
        if not verify_password(password_data.current_password, user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="當前密碼錯誤"
            )
        
        # 更新密碼
        user.password_hash = get_password_hash(password_data.new_password)
        
        await self.db.commit()
        await self.db.refresh(user)
        
        return user
    
    async def update_avatar(self, user: User, avatar_url: str) -> User:
        """
        更新用戶頭像
        
        Args:
            user: 用戶對象
            avatar_url: 頭像 URL
            
        Returns:
            User: 更新後的用戶對象
        """
        user.avatar_url = avatar_url
        
        await self.db.commit()
        await self.db.refresh(user)
        
        return user
    
    async def deactivate_user(self, user: User) -> User:
        """
        停用用戶帳號
        
        Args:
            user: 用戶對象
            
        Returns:
            User: 更新後的用戶對象
        """
        user.is_active = False
        
        await self.db.commit()
        await self.db.refresh(user)
        
        return user
    
    async def activate_user(self, user: User) -> User:
        """
        啟用用戶帳號
        
        Args:
            user: 用戶對象
            
        Returns:
            User: 更新後的用戶對象
        """
        user.is_active = True
        
        await self.db.commit()
        await self.db.refresh(user)
        
        return user
