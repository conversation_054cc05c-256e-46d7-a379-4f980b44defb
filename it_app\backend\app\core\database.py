"""
數據庫配置和連接管理
使用 SQLAlchemy 2.0 異步模式
"""

from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase

from .config import get_settings

settings = get_settings()

# 創建異步數據庫引擎
engine = create_async_engine(
    settings.database_url,
    echo=settings.debug,  # 在調試模式下顯示 SQL 語句
    future=True,
    pool_pre_ping=True,  # 連接池預檢查
)

# 創建異步會話工廠
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False,
)


class Base(DeclarativeBase):
    """SQLAlchemy 聲明式基類"""
    pass


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    獲取數據庫會話
    FastAPI 依賴注入函數
    
    Yields:
        AsyncSession: 數據庫會話
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_db() -> None:
    """
    初始化數據庫
    創建所有表
    """
    from app.models import user, service  # 導入所有模型
    
    async with engine.begin() as conn:
        # 創建所有表
        await conn.run_sync(Base.metadata.create_all)


async def close_db() -> None:
    """
    關閉數據庫連接
    """
    await engine.dispose()
