import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { User, Lock, Eye, EyeOff, AlertCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import MobileLayout from '@/components/layout/MobileLayout';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { state, login, clearError } = useAuth();
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);

  // 如果已經登錄，重定向到首頁
  useEffect(() => {
    if (state.isAuthenticated) {
      navigate('/', { replace: true });
    }
  }, [state.isAuthenticated, navigate]);

  // 清除錯誤信息
  useEffect(() => {
    return () => clearError();
  }, [clearError]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // 輸入時清除錯誤
    if (state.error) {
      clearError();
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // 基本驗證
    if (!formData.username.trim()) {
      return;
    }
    if (!formData.password.trim()) {
      return;
    }

    try {
      await login(formData);
    } catch (error) {
      // 錯誤由Context處理
    }
  };

  const handleThirdPartyLogin = (platform: string) => {
    // 模擬第三方登錄流程
    console.log(`第三方登錄: ${platform}`);
    // 實際實現中這裡會跳轉到第三方認證頁面
  };

  return (
    <MobileLayout showNavBar={false}>
      <div className="flex flex-col h-full bg-gradient-to-br from-primary-50 to-white">
        {/* 頭部區域 */}
        <div className="flex-shrink-0 px-6 pt-8 pb-4">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              AI速應
            </h1>
            <p className="text-gray-600">
              歡迎回來，請登錄您的帳戶
            </p>
          </div>
        </div>

        {/* 主要內容區域 */}
        <div className="flex-1 px-6 py-4">
          <div className="bg-white rounded-2xl shadow-lg p-6">
            {/* 錯誤提示 */}
            {state.error && (
              <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start">
                <AlertCircle className="w-5 h-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                <span className="text-red-700 text-sm">{state.error}</span>
              </div>
            )}

            {/* 登錄表單 */}
            <form onSubmit={handleSubmit} className="space-y-4">
              <Input
                name="username"
                type="text"
                placeholder="請輸入用戶名或手機號"
                value={formData.username}
                onChange={handleInputChange}
                leftIcon={<User />}
                required
              />

              <Input
                name="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="請輸入密碼"
                value={formData.password}
                onChange={handleInputChange}
                leftIcon={<Lock />}
                rightIcon={
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff /> : <Eye />}
                  </button>
                }
                required
              />

              <div className="flex items-center justify-between text-sm">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 focus:ring-2"
                  />
                  <span className="ml-2 text-gray-600">記住我</span>
                </label>
                <Link 
                  to="/forgot-password" 
                  className="text-primary-600 hover:text-primary-700 font-medium"
                >
                  忘記密碼？
                </Link>
              </div>

              <Button
                type="submit"
                className="w-full"
                loading={state.loading}
                size="lg"
              >
                登錄
              </Button>
            </form>

            {/* 分隔線 */}
            <div className="my-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">或者</span>
                </div>
              </div>
            </div>

            {/* 第三方登錄 */}
            <div className="space-y-3">
              <Button
                variant="outline"
                className="w-full"
                onClick={() => handleThirdPartyLogin('wechat')}
              >
                <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="#07C160">
                  <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.16c-.1.303-.026.435.259.284l1.481-.735a.657.657 0 0 1 .75.075c1.027.641 2.25.954 3.376.954 4.8 0 8.691-3.288 8.691-7.342 0-4.054-3.891-7.342-8.691-7.342z"/>
                  <path d="M13.725 13.532c-.016 2.969-2.407 5.37-5.376 5.37-.67 0-1.314-.1-1.902-.286l-1.815.9c-.357.178-.617-.133-.454-.539l.54-1.346c-1.527-1.152-2.528-2.985-2.528-5.099 0-3.542 2.869-6.411 6.411-6.411 3.243 0 5.917 2.406 6.346 5.513.299-.036.606-.055.918-.055 3.542 0 6.411 2.869 6.411 6.411 0 1.847-.801 3.514-2.075 4.691l.434 1.026c.15.357-.108.646-.434.485l-1.423-.694c-.52.163-1.072.247-1.643.247-1.877 0-3.555-.809-4.733-2.098.108-.379.163-.777.163-1.186 0-2.969-2.407-5.376-5.376-5.376-.294 0-.582.02-.864.059z"/>
                </svg>
                微信登錄
              </Button>

              <Button
                variant="outline"
                className="w-full"
                onClick={() => handleThirdPartyLogin('qq')}
              >
                <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="#12B7F5">
                  <path d="M12.001 2.5c-4.688 0-8.5 3.812-8.5 8.5s3.812 8.5 8.5 8.5 8.5-3.812 8.5-8.5-3.812-8.5-8.5-8.5zm4.659 14.671c-.645-.315-1.095-.98-1.095-1.75 0-.316.077-.614.212-.875.045-.087.098-.17.155-.25.629-1.123.959-2.407.959-3.796 0-4.136-3.364-7.5-7.5-7.5s-7.5 3.364-7.5 7.5c0 1.389.33 2.673.959 3.796.057.08.11.163.155.25.135.261.212.559.212.875 0 .77-.45 1.435-1.095 1.75-.381.185-.659.547-.659.975 0 .597.483 1.08 1.08 1.08h13.236c.597 0 1.08-.483 1.08-1.08 0-.428-.278-.79-.659-.975z"/>
                </svg>
                QQ登錄
              </Button>
            </div>

            {/* 註冊鏈接 */}
            <div className="mt-6 text-center">
              <span className="text-gray-600">還沒有帳戶？</span>
              <Link 
                to="/register" 
                className="ml-1 text-primary-600 hover:text-primary-700 font-medium"
              >
                立即註冊
              </Link>
            </div>
          </div>
        </div>
      </div>
    </MobileLayout>
  );
};

export default LoginPage; 