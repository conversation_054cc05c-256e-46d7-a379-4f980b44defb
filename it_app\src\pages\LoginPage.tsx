import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { User, Lock, Eye, EyeOff, AlertCircle, Mail } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import MobileLayout from '@/components/layout/MobileLayout';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { state, login, clearError } = useAuth();
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);

  // 如果已經登錄，重定向到首頁
  useEffect(() => {
    if (state.isAuthenticated) {
      navigate('/', { replace: true });
    }
  }, [state.isAuthenticated, navigate]);

  // 清除錯誤信息
  useEffect(() => {
    return () => clearError();
  }, [clearError]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // 輸入時清除錯誤
    if (state.error) {
      clearError();
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // 基本驗證
    if (!formData.username.trim()) {
      return;
    }
    if (!formData.password.trim()) {
      return;
    }

    try {
      await login(formData);
    } catch (error) {
      // 錯誤由Context處理
    }
  };

  const handleThirdPartyLogin = (platform: string) => {
    // 模擬第三方登錄流程
    console.log(`第三方登錄: ${platform}`);
    // 實際實現中這裡會跳轉到第三方認證頁面
    if (platform === 'line') {
      // LINE 登入邏輯
      window.location.href = 'https://access.line.me/oauth2/v2.1/authorize?...';
    } else if (platform === 'email') {
      // Email 快速登入邏輯
      navigate('/email-login');
    }
  };

  return (
    <MobileLayout showNavBar={false}>
      <div className="flex flex-col h-full bg-gradient-to-br from-primary-50 to-white">
        {/* 頭部區域 */}
        <div className="flex-shrink-0 px-6 pt-8 pb-4">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              AI速應
            </h1>
            <p className="text-gray-600">
              歡迎回來，請登錄您的帳戶
            </p>
          </div>
        </div>

        {/* 主要內容區域 */}
        <div className="flex-1 px-6 py-4">
          <div className="bg-white rounded-2xl shadow-lg p-6">
            {/* 錯誤提示 */}
            {state.error && (
              <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start">
                <AlertCircle className="w-5 h-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                <span className="text-red-700 text-sm">{state.error}</span>
              </div>
            )}

            {/* 登錄表單 */}
            <form onSubmit={handleSubmit} className="space-y-4">
              <Input
                name="username"
                type="text"
                placeholder="請輸入用戶名或手機號"
                value={formData.username}
                onChange={handleInputChange}
                leftIcon={<User />}
                required
              />

              <Input
                name="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="請輸入密碼"
                value={formData.password}
                onChange={handleInputChange}
                leftIcon={<Lock />}
                rightIcon={
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff /> : <Eye />}
                  </button>
                }
                required
              />

              <div className="flex items-center justify-between text-sm">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 focus:ring-2"
                  />
                  <span className="ml-2 text-gray-600">記住我</span>
                </label>
                <Link 
                  to="/forgot-password" 
                  className="text-primary-600 hover:text-primary-700 font-medium"
                >
                  忘記密碼？
                </Link>
              </div>

              <Button
                type="submit"
                className="w-full"
                loading={state.loading}
                size="lg"
              >
                登錄
              </Button>
            </form>

            {/* 分隔線 */}
            <div className="my-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">或者</span>
                </div>
              </div>
            </div>

            {/* 第三方登錄 */}
            <div className="space-y-3">
              <Button
                variant="outline"
                className="w-full"
                onClick={() => handleThirdPartyLogin('line')}
              >
                <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="#00B900">
                  <path d="M19.365 9.863c.349 0 .63.285.63.631 0 .345-.281.63-.63.63H17.61v1.125h1.755c.349 0 .63.283.63.63 0 .344-.281.629-.63.629h-2.386c-.345 0-.627-.285-.627-.629V8.108c0-.345.282-.63.627-.63h2.386c.349 0 .63.285.63.63 0 .349-.281.63-.63.63H17.61v1.125h1.755zm-3.855 3.016c0 .27-.174.51-.432.596-.064.021-.133.031-.199.031-.211 0-.391-.09-.51-.25l-2.443-3.317v2.94c0 .344-.279.629-.631.629-.346 0-.626-.285-.626-.629V8.108c0-.27.173-.51.43-.595.06-.023.136-.033.194-.033.195 0 .375.104.495.254l2.462 3.33V8.108c0-.345.282-.63.63-.63.345 0 .63.285.63.63v4.771zm-5.741 0c0 .344-.282.629-.631.629-.345 0-.627-.285-.627-.629V8.108c0-.345.282-.63.627-.63.349 0 .631.285.631.63v4.771zm-2.466.629H4.917c-.345 0-.63-.285-.63-.629V8.108c0-.345.285-.63.63-.63.348 0 .63.285.63.63v4.141h1.756c.348 0 .629.283.629.63 0 .344-.281.629-.629.629M24 10.314C24 4.943 18.615.572 12 .572S0 4.943 0 10.314c0 4.811 4.27 8.842 10.035 9.608.391.082.923.258 1.058.59.12.301.079.766.038 1.08l-.164 1.02c-.045.301-.24 1.186 1.049.645 1.291-.539 6.916-4.078 9.436-6.975C23.176 14.393 24 12.458 24 10.314"/>
                </svg>
                LINE 登錄
              </Button>

              <Button
                variant="outline"
                className="w-full"
                onClick={() => handleThirdPartyLogin('email')}
              >
                <Mail className="w-5 h-5 mr-2 text-blue-600" />
                Email 快速登錄
              </Button>
            </div>

            {/* 註冊鏈接 */}
            <div className="mt-6 text-center">
              <span className="text-gray-600">還沒有帳戶？</span>
              <Link 
                to="/register" 
                className="ml-1 text-primary-600 hover:text-primary-700 font-medium"
              >
                立即註冊
              </Link>
            </div>
          </div>
        </div>
      </div>
    </MobileLayout>
  );
};

export default LoginPage; 