"""
認證相關 Pydantic 模型
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr, Field, validator
import re


class UserRegister(BaseModel):
    """用戶註冊模型"""
    
    username: str = Field(
        ..., 
        min_length=3, 
        max_length=50,
        description="用戶名（3-50字符，字母數字下劃線）"
    )
    email: EmailStr = Field(..., description="電子郵件")
    password: str = Field(
        ..., 
        min_length=8, 
        max_length=128,
        description="密碼（8-128字符，包含大小寫字母和數字）"
    )
    nickname: Optional[str] = Field(
        None, 
        min_length=1, 
        max_length=100,
        description="暱稱（可選，1-100字符）"
    )
    
    @validator("username")
    def validate_username(cls, v):
        """驗證用戶名格式"""
        if not re.match(r"^[a-zA-Z0-9_]+$", v):
            raise ValueError("用戶名只能包含字母、數字和下劃線")
        return v
    
    @validator("password")
    def validate_password(cls, v):
        """驗證密碼強度"""
        if not re.search(r"[a-z]", v):
            raise ValueError("密碼必須包含小寫字母")
        if not re.search(r"[A-Z]", v):
            raise ValueError("密碼必須包含大寫字母")
        if not re.search(r"\d", v):
            raise ValueError("密碼必須包含數字")
        return v


class UserLogin(BaseModel):
    """用戶登入模型"""
    
    username: str = Field(..., description="用戶名或郵箱")
    password: str = Field(..., description="密碼")


class EmailCodeSend(BaseModel):
    """發送郵箱驗證碼模型"""
    
    email: EmailStr = Field(..., description="電子郵件")


class EmailCodeVerify(BaseModel):
    """驗證郵箱驗證碼模型"""
    
    email: EmailStr = Field(..., description="電子郵件")
    code: str = Field(
        ..., 
        min_length=6, 
        max_length=6,
        description="6位驗證碼"
    )
    
    @validator("code")
    def validate_code(cls, v):
        """驗證驗證碼格式"""
        if not v.isdigit():
            raise ValueError("驗證碼必須是6位數字")
        return v


class TokenResponse(BaseModel):
    """令牌響應模型"""
    
    access_token: str = Field(..., description="訪問令牌")
    refresh_token: str = Field(..., description="刷新令牌")
    token_type: str = Field(default="bearer", description="令牌類型")
    expires_in: int = Field(..., description="過期時間（秒）")


class UserResponse(BaseModel):
    """用戶響應模型"""
    
    id: int = Field(..., description="用戶ID")
    username: str = Field(..., description="用戶名")
    email: str = Field(..., description="電子郵件")
    nickname: Optional[str] = Field(None, description="暱稱")
    avatar_url: Optional[str] = Field(None, description="頭像URL")
    is_active: bool = Field(..., description="帳號狀態")
    login_provider: str = Field(..., description="登入方式")
    created_at: datetime = Field(..., description="創建時間")
    updated_at: datetime = Field(..., description="更新時間")
    
    class Config:
        from_attributes = True
