"""
AI 案例業務邏輯服務
"""

from typing import List, Optional, Tuple, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, desc, and_
from sqlalchemy.orm import selectinload
from fastapi import HTTPException, status

from app.models.case import AICase, CaseTestimonial
from app.schemas.common import PaginationParams


class CaseService:
    """案例服務類"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_cases(
        self,
        pagination: PaginationParams,
        category: Optional[str] = None,
        technology: Optional[str] = None,
        industry: Optional[str] = None,
        featured_only: bool = False,
        search: Optional[str] = None
    ) -> Tuple[List[AICase], int]:
        """
        獲取案例列表
        
        Args:
            pagination: 分頁參數
            category: 分類篩選
            technology: 技術篩選
            industry: 行業篩選
            featured_only: 只顯示精選案例
            search: 搜索關鍵詞
            
        Returns:
            Tuple[List[AICase], int]: 案例列表和總數
        """
        # 構建查詢
        query = select(AICase).where(AICase.is_published == True)
        
        # 分類篩選
        if category:
            query = query.where(AICase.category == category)
        
        # 技術篩選
        if technology:
            query = query.where(AICase.technologies.contains([technology]))
        
        # 行業篩選
        if industry:
            query = query.where(AICase.industry == industry)
        
        # 精選篩選
        if featured_only:
            query = query.where(AICase.is_featured == True)
        
        # 搜索篩選
        if search:
            search_pattern = f"%{search}%"
            query = query.where(
                (AICase.title.ilike(search_pattern)) |
                (AICase.description.ilike(search_pattern)) |
                (AICase.client_name.ilike(search_pattern))
            )
        
        # 計算總數
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 排序和分頁
        query = query.order_by(
            desc(AICase.is_featured),
            desc(AICase.created_at)
        ).offset(pagination.offset).limit(pagination.size)
        
        result = await self.db.execute(query)
        cases = result.scalars().all()
        
        return cases, total
    
    async def get_case_by_id(self, case_id: int) -> Optional[AICase]:
        """
        根據 ID 獲取案例詳情
        
        Args:
            case_id: 案例 ID
            
        Returns:
            AICase: 案例對象或 None
        """
        stmt = select(AICase).options(
            selectinload(AICase.testimonials)
        ).where(
            and_(AICase.id == case_id, AICase.is_published == True)
        )
        
        result = await self.db.execute(stmt)
        case = result.scalar_one_or_none()
        
        if case:
            # 增加瀏覽次數
            case.view_count += 1
            await self.db.commit()
        
        return case
    
    async def get_featured_cases(self, limit: int = 6) -> List[AICase]:
        """
        獲取精選案例
        
        Args:
            limit: 限制數量
            
        Returns:
            List[AICase]: 精選案例列表
        """
        stmt = select(AICase).where(
            and_(AICase.is_featured == True, AICase.is_published == True)
        ).order_by(desc(AICase.created_at)).limit(limit)
        
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_case_categories(self) -> List[Dict[str, Any]]:
        """
        獲取案例分類統計
        
        Returns:
            List[Dict]: 分類統計列表
        """
        stmt = select(
            AICase.category,
            func.count(AICase.id).label('count')
        ).where(AICase.is_published == True).group_by(AICase.category)
        
        result = await self.db.execute(stmt)
        categories = result.all()
        
        # 分類映射
        category_mapping = {
            "ai-platform": "AI平台開發",
            "ai-search": "智能搜索",
            "ai-media": "媒體處理",
            "ai-chat": "對話系統",
            "ai-vision": "計算機視覺",
            "ai-analytics": "數據分析",
            "ai-automation": "智能自動化",
            "ai-recommendation": "推薦系統"
        }
        
        return [
            {
                "category": category,
                "count": count,
                "display_name": category_mapping.get(category, category)
            }
            for category, count in categories
        ]
    
    async def get_case_technologies(self) -> List[Dict[str, Any]]:
        """
        獲取案例技術統計
        
        Returns:
            List[Dict]: 技術統計列表
        """
        # 由於 SQLite 對 JSON 查詢支持有限，這裡使用 Python 處理
        stmt = select(AICase.technologies).where(
            and_(AICase.is_published == True, AICase.technologies.isnot(None))
        )
        
        result = await self.db.execute(stmt)
        all_technologies = result.scalars().all()
        
        # 統計技術使用次數
        tech_count = {}
        for tech_list in all_technologies:
            if tech_list:
                for tech in tech_list:
                    tech_count[tech] = tech_count.get(tech, 0) + 1
        
        # 技術映射
        tech_mapping = {
            "RAG": "檢索增強生成",
            "Dify": "Dify平台",
            "ChatGPT": "ChatGPT",
            "LangChain": "LangChain",
            "Vector Database": "向量數據庫",
            "Computer Vision": "計算機視覺",
            "NLP": "自然語言處理",
            "Machine Learning": "機器學習",
            "Deep Learning": "深度學習",
            "TensorFlow": "TensorFlow",
            "PyTorch": "PyTorch"
        }
        
        return [
            {
                "technology": tech,
                "count": count,
                "display_name": tech_mapping.get(tech, tech)
            }
            for tech, count in sorted(tech_count.items(), key=lambda x: x[1], reverse=True)
        ]
    
    async def get_case_stats(self) -> Dict[str, Any]:
        """
        獲取案例統計信息
        
        Returns:
            Dict: 統計信息
        """
        # 總案例數
        total_stmt = select(func.count(AICase.id)).where(AICase.is_published == True)
        total_result = await self.db.execute(total_stmt)
        total_cases = total_result.scalar()
        
        # 精選案例數
        featured_stmt = select(func.count(AICase.id)).where(
            and_(AICase.is_published == True, AICase.is_featured == True)
        )
        featured_result = await self.db.execute(featured_stmt)
        featured_cases = featured_result.scalar()
        
        # 總瀏覽量
        views_stmt = select(func.sum(AICase.view_count)).where(AICase.is_published == True)
        views_result = await self.db.execute(views_stmt)
        total_views = views_result.scalar() or 0
        
        # 平均評分
        rating_stmt = select(func.avg(AICase.client_rating)).where(
            and_(AICase.is_published == True, AICase.client_rating.isnot(None))
        )
        rating_result = await self.db.execute(rating_stmt)
        average_rating = rating_result.scalar()
        
        # 獲取分類和技術統計
        categories = await self.get_case_categories()
        technologies = await self.get_case_technologies()
        
        return {
            "total_cases": total_cases,
            "featured_cases": featured_cases,
            "total_views": total_views,
            "average_rating": round(float(average_rating), 2) if average_rating else None,
            "categories": categories,
            "technologies": technologies[:10]  # 只返回前10個技術
        }
