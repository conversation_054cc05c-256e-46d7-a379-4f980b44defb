/**
 * 結構化數據生成器
 * 為 SEO 優化生成 JSON-LD 格式的結構化數據
 */

interface Organization {
  "@type": "Organization";
  name: string;
  url: string;
  logo: string;
  description: string;
  contactPoint: {
    "@type": "ContactPoint";
    telephone: string;
    contactType: string;
    availableLanguage: string[];
  };
  sameAs: string[];
}

interface WebSite {
  "@type": "WebSite";
  name: string;
  url: string;
  description: string;
  potentialAction: {
    "@type": "SearchAction";
    target: string;
    "query-input": string;
  };
}

interface Service {
  "@type": "Service";
  name: string;
  description: string;
  provider: Organization;
  serviceType: string;
  areaServed: string;
  hasOfferCatalog: {
    "@type": "OfferCatalog";
    name: string;
    itemListElement: Array<{
      "@type": "Offer";
      itemOffered: {
        "@type": "Service";
        name: string;
        description: string;
      };
    }>;
  };
}

interface Article {
  "@type": "Article";
  headline: string;
  description: string;
  image: string[];
  datePublished: string;
  dateModified: string;
  author: {
    "@type": "Organization";
    name: string;
  };
  publisher: Organization;
  mainEntityOfPage: {
    "@type": "WebPage";
    "@id": string;
  };
}

export class StructuredDataGenerator {
  private static baseUrl = window.location.origin;
  
  /**
   * 生成組織結構化數據
   */
  static generateOrganization(): Organization {
    return {
      "@type": "Organization",
      name: "AI速應",
      url: this.baseUrl,
      logo: `${this.baseUrl}/images/logo.png`,
      description: "專業的 AI 解決方案平台，提供智能客服、數據分析、計算機視覺等 AI 技術服務",
      contactPoint: {
        "@type": "ContactPoint",
        telephone: "+886-2-1234-5678",
        contactType: "customer service",
        availableLanguage: ["zh-TW", "en"]
      },
      sameAs: [
        "https://www.facebook.com/aisuyingplatform",
        "https://www.linkedin.com/company/aisuying",
        "https://twitter.com/aisuyingtech"
      ]
    };
  }

  /**
   * 生成網站結構化數據
   */
  static generateWebSite(): WebSite {
    return {
      "@type": "WebSite",
      name: "AI速應",
      url: this.baseUrl,
      description: "探索最新的 AI 技術解決方案，查看成功案例，了解 AI 如何改變您的業務",
      potentialAction: {
        "@type": "SearchAction",
        target: `${this.baseUrl}/search?q={search_term_string}`,
        "query-input": "required name=search_term_string"
      }
    };
  }

  /**
   * 生成服務結構化數據
   */
  static generateService(): Service {
    const organization = this.generateOrganization();
    
    return {
      "@type": "Service",
      name: "AI 技術解決方案",
      description: "提供全方位的 AI 技術服務，包括智能客服、數據分析、計算機視覺等",
      provider: organization,
      serviceType: "AI Technology Solutions",
      areaServed: "Taiwan",
      hasOfferCatalog: {
        "@type": "OfferCatalog",
        name: "AI 服務目錄",
        itemListElement: [
          {
            "@type": "Offer",
            itemOffered: {
              "@type": "Service",
              name: "智能客服系統",
              description: "24/7 AI 客服機器人，提升客戶服務效率"
            }
          },
          {
            "@type": "Offer",
            itemOffered: {
              "@type": "Service",
              name: "數據分析平台",
              description: "智能數據分析和商業洞察服務"
            }
          },
          {
            "@type": "Offer",
            itemOffered: {
              "@type": "Service",
              name: "計算機視覺",
              description: "圖像識別和視覺分析解決方案"
            }
          }
        ]
      }
    };
  }

  /**
   * 生成案例文章結構化數據
   */
  static generateCaseArticle(caseData: {
    title: string;
    description: string;
    image?: string;
    publishDate: string;
    modifiedDate: string;
    url: string;
  }): Article {
    const organization = this.generateOrganization();
    
    return {
      "@type": "Article",
      headline: caseData.title,
      description: caseData.description,
      image: caseData.image ? [caseData.image] : [`${this.baseUrl}/images/default-case.jpg`],
      datePublished: caseData.publishDate,
      dateModified: caseData.modifiedDate,
      author: {
        "@type": "Organization",
        name: "AI速應"
      },
      publisher: organization,
      mainEntityOfPage: {
        "@type": "WebPage",
        "@id": caseData.url
      }
    };
  }

  /**
   * 生成首頁結構化數據
   */
  static generateHomePage() {
    return {
      "@context": "https://schema.org",
      "@graph": [
        this.generateOrganization(),
        this.generateWebSite(),
        this.generateService()
      ]
    };
  }

  /**
   * 生成服務頁面結構化數據
   */
  static generateServicesPage() {
    return {
      "@context": "https://schema.org",
      "@graph": [
        this.generateOrganization(),
        this.generateService()
      ]
    };
  }

  /**
   * 生成案例頁面結構化數據
   */
  static generateCasesPage() {
    return {
      "@context": "https://schema.org",
      "@graph": [
        this.generateOrganization(),
        {
          "@type": "CollectionPage",
          name: "AI 成功案例",
          description: "查看我們的 AI 項目成功案例，了解 AI 技術如何改變各行各業",
          url: `${this.baseUrl}/cases`,
          mainEntity: {
            "@type": "ItemList",
            name: "AI 案例列表",
            description: "展示各種 AI 技術應用的成功案例"
          }
        }
      ]
    };
  }

  /**
   * 生成案例詳情頁結構化數據
   */
  static generateCaseDetailPage(caseData: {
    title: string;
    description: string;
    image?: string;
    publishDate: string;
    modifiedDate: string;
    url: string;
    technologies?: string[];
    industry?: string;
  }) {
    return {
      "@context": "https://schema.org",
      "@graph": [
        this.generateOrganization(),
        this.generateCaseArticle(caseData),
        {
          "@type": "TechArticle",
          headline: caseData.title,
          description: caseData.description,
          about: caseData.technologies?.map(tech => ({
            "@type": "Thing",
            name: tech
          })) || [],
          industry: caseData.industry,
          applicationCategory: "AI Technology"
        }
      ]
    };
  }
}
