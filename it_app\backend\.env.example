# 應用配置
APP_NAME=AI速應後端API
APP_VERSION=1.0.0
DEBUG=True
ENVIRONMENT=development

# 服務器配置
HOST=0.0.0.0
PORT=8000

# 數據庫配置
DATABASE_URL=sqlite+aiosqlite:///./app.db

# JWT 配置
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=7

# 密碼加密配置
BCRYPT_ROUNDS=12

# CORS 配置
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Email 配置 (用於驗證碼發送)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# LINE 登入配置
LINE_CHANNEL_ID=your-line-channel-id
LINE_CHANNEL_SECRET=your-line-channel-secret
LINE_REDIRECT_URI=http://localhost:8000/api/auth/line/callback

# 速率限制配置
RATE_LIMIT_PER_MINUTE=100

# 日誌配置
LOG_LEVEL=INFO
LOG_FORMAT=json
