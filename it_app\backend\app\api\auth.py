"""
認證相關 API 路由
"""

from typing import Any
from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.dependencies import get_auth_service, get_email_service, get_current_active_user
from app.schemas.auth import (
    UserRegister, 
    UserLogin, 
    EmailCodeSend, 
    EmailCodeVerify,
    TokenResponse,
    UserResponse
)
from app.schemas.common import ResponseModel
from app.services.auth_service import AuthService
from app.services.user_service import UserService
from app.services.email_service import EmailService
from app.models.user import User

router = APIRouter()


@router.post("/register", response_model=ResponseModel)
async def register(
    user_data: UserRegister,
    request: Request,
    auth_service: AuthService = Depends(get_auth_service)
) -> Any:
    """
    用戶註冊
    """
    try:
        user = await auth_service.register_user(user_data)
        
        # 記錄註冊日誌
        await auth_service.log_login(
            user=user,
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent"),
            login_provider="email",
            success=True
        )
        
        return ResponseModel(
            success=True,
            message="註冊成功",
            data={"user_id": user.id}
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="註冊失敗，請稍後重試"
        )


@router.post("/login", response_model=ResponseModel)
async def login(
    user_data: UserLogin,
    request: Request,
    auth_service: AuthService = Depends(get_auth_service)
) -> Any:
    """
    用戶登入
    """
    # 驗證用戶憑據
    user = await auth_service.authenticate_user(
        user_data.username, 
        user_data.password
    )
    
    if not user:
        # 記錄失敗的登入嘗試
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用戶名或密碼錯誤"
        )
    
    # 創建令牌
    tokens = await auth_service.create_tokens(user)
    
    # 記錄登入日誌
    await auth_service.log_login(
        user=user,
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent"),
        login_provider="email",
        success=True
    )
    
    return ResponseModel(
        success=True,
        message="登入成功",
        data=tokens
    )


@router.post("/email-code/send", response_model=ResponseModel)
async def send_email_code(
    email_data: EmailCodeSend,
    email_service: EmailService = Depends(get_email_service)
) -> Any:
    """
    發送郵箱驗證碼
    """
    try:
        code = await email_service.send_verification_code(email_data.email)
        
        return ResponseModel(
            success=True,
            message="驗證碼已發送到您的郵箱",
            data={"email": email_data.email}
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="發送驗證碼失敗，請稍後重試"
        )


@router.post("/email-code/verify", response_model=ResponseModel)
async def verify_email_code(
    verify_data: EmailCodeVerify,
    request: Request,
    auth_service: AuthService = Depends(get_auth_service),
    email_service: EmailService = Depends(get_email_service),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    驗證郵箱驗證碼並登入
    """
    # 驗證驗證碼
    if not email_service.verify_code(verify_data.email, verify_data.code):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="驗證碼錯誤或已過期"
        )
    
    # 查找或創建用戶
    user_service = UserService(db)
    user = await user_service.get_user_by_email(verify_data.email)
    
    if not user:
        # 如果用戶不存在，創建新用戶
        from app.schemas.auth import UserRegister
        from app.core.security import generate_random_string
        
        register_data = UserRegister(
            username=verify_data.email.split('@')[0],
            email=verify_data.email,
            password=generate_random_string(16),  # 隨機密碼
            nickname=verify_data.email.split('@')[0]
        )
        
        user = await auth_service.register_user(register_data)
        user.login_provider = "email_code"
        await db.commit()
    
    # 創建令牌
    tokens = await auth_service.create_tokens(user)
    
    # 記錄登入日誌
    await auth_service.log_login(
        user=user,
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent"),
        login_provider="email_code",
        success=True
    )
    
    return ResponseModel(
        success=True,
        message="驗證成功，登入完成",
        data=tokens
    )


@router.get("/me", response_model=ResponseModel)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    獲取當前用戶信息
    """
    user_data = UserResponse.model_validate(current_user)
    
    return ResponseModel(
        success=True,
        message="獲取用戶信息成功",
        data=user_data
    )


@router.post("/refresh", response_model=ResponseModel)
async def refresh_token(
    # TODO: 實現刷新令牌邏輯
) -> Any:
    """
    刷新訪問令牌
    """
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="刷新令牌功能尚未實現"
    )


@router.post("/logout", response_model=ResponseModel)
async def logout(
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    用戶登出
    """
    # TODO: 實現令牌黑名單機制
    
    return ResponseModel(
        success=True,
        message="登出成功"
    )


@router.post("/line/callback", response_model=ResponseModel)
async def line_login_callback(
    # TODO: 實現 LINE 登入回調
) -> Any:
    """
    LINE 登入回調處理
    """
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="LINE 登入功能尚未實現"
    )
